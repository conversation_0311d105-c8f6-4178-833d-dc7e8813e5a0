import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  ImageBackground,
  TextInput,
  Image,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  BounceIn,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { userProfile } from '../constants/UserData';
import { useProfile } from '../contexts/ProfileContext';
import { Colors, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress, NutritionProgress } from '../components/CircularProgress';
import HealthMonitor from '../components/HealthMonitor';

import * as Haptics from 'expo-haptics';

const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const { profile, dailyData, incrementWater, getProgressPercentage, getRecentMeals } = useProfile();
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [mealSuggestions, setMealSuggestions] = useState<any[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);

  // Animated values for micro-interactions
  const pulseValue = useSharedValue(1);
  const waterWaveValue = useSharedValue(0);

  useEffect(() => {
    // Update time every minute
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);

    // Pulse animation for active elements
    pulseValue.value = withRepeat(
      withSequence(
        withSpring(1.05, { duration: 1000 }),
        withSpring(1, { duration: 1000 })
      ),
      -1,
      true
    );

    // Water wave animation
    waterWaveValue.value = withRepeat(
      withSequence(
        withSpring(1, { duration: 2000 }),
        withSpring(0.8, { duration: 2000 })
      ),
      -1,
      true
    );

    return () => clearInterval(timer);
  }, []);

  // Load profile image and meal suggestions
  useEffect(() => {
    const loadProfileImage = async () => {
      try {
        const savedImage = await AsyncStorage.getItem('profileImage');
        if (savedImage) {
          setProfileImage(savedImage);
        }
      } catch (error) {
        console.error('Error loading profile image:', error);
      }
    };

    const loadMealSuggestions = async () => {
      try {
        const savedSuggestions = await AsyncStorage.getItem('mealSuggestions');
        if (savedSuggestions) {
          setMealSuggestions(JSON.parse(savedSuggestions));
        } else {
          generateMealSuggestions();
        }
      } catch (error) {
        console.error('Error loading meal suggestions:', error);
        generateMealSuggestions();
      }
    };

    loadProfileImage();
    loadMealSuggestions();
  }, []);

  // Refresh meal suggestions when profile changes
  useEffect(() => {
    if (profile.isProfileComplete) {
      generateMealSuggestions();
    }
  }, [profile.caloriesGoal, profile.proteinGoal, profile.dietaryPreferences]);

  const onRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1200);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const addWater = () => {
    incrementWater();
  };



  const waterWaveStyle = useAnimatedStyle(() => ({
    transform: [{ scale: waterWaveValue.value }],
  }));

  const quickActions = [
    {
      icon: 'scan' as const,
      lottieIcon: 'scanner',
      label: 'Smart Scan',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Scanner');
      },
    },
    {
      icon: 'restaurant' as const,
      lottieIcon: 'recipes',
      label: 'AI Recipes',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Recipes');
      },
    },
    {
      icon: 'calendar' as const,
      lottieIcon: 'plan',
      label: 'Meal Plan',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Plan');
      },
    },
    {
      icon: 'chatbubble-ellipses' as const,
      lottieIcon: 'aiThinking',
      label: 'Ask AI',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Ask');
      },
    },
  ];

  const nutritionData = [
    {
      label: 'Calories',
      value: dailyData.caloriesConsumed,
      target: profile.caloriesGoal,
      unit: 'kcal',
      icon: 'flame',
      lottieIcon: 'fire',
      color: '#FF6B35',
      bgColor: 'rgba(255, 107, 53, 0.1)',
    },
    {
      label: 'Protein',
      value: dailyData.proteinConsumed,
      target: profile.proteinGoal,
      unit: 'g',
      icon: 'fitness',
      lottieIcon: 'heartbeat',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
    },
    {
      label: 'Carbs',
      value: Math.round((dailyData.caloriesConsumed * profile.macroTargets.carbs / 100) / 4),
      target: Math.round((profile.caloriesGoal * profile.macroTargets.carbs / 100) / 4),
      unit: 'g',
      icon: 'leaf',
      lottieIcon: 'broccoli',
      color: '#4CAF50',
      bgColor: 'rgba(76, 175, 80, 0.1)',
    },
    {
      label: 'Fat',
      value: Math.round((dailyData.caloriesConsumed * profile.macroTargets.fat / 100) / 9),
      target: Math.round((profile.caloriesGoal * profile.macroTargets.fat / 100) / 9),
      unit: 'g',
      icon: 'water',
      lottieIcon: 'avocado',
      color: '#FFC107',
      bgColor: 'rgba(255, 193, 7, 0.1)',
    },
  ];

  // Get real recent meals from AsyncStorage
  const recentMeals = getRecentMeals().map(meal => ({
    ...meal,
    icon: meal.type === 'breakfast' ? 'sunny' :
          meal.type === 'lunch' ? 'restaurant' :
          meal.type === 'dinner' ? 'moon' : 'cafe'
  }));

  // Generate AI meal suggestions based on user profile
  const generateMealSuggestions = async () => {
    if (!profile.isProfileComplete) {
      setMealSuggestions([]);
      return;
    }

    setLoadingSuggestions(true);

    try {
      // In a real implementation, this would call an AI API with the user's profile data
      // For now, we'll simulate an API call with a timeout
      setTimeout(() => {
        const remainingCalories = profile.caloriesGoal - dailyData.caloriesConsumed;
        const suggestions = [];

        // Generate suggestions based on user profile
        if (remainingCalories > 300) {
          suggestions.push({
            id: 'meal1',
            name: 'Grilled Salmon with Asparagus',
            prep: '25 min',
            difficulty: 'Medium',
            calories: Math.min(remainingCalories, 450)
          });
        }

        if (dailyData.proteinConsumed < profile.proteinGoal) {
          suggestions.push({
            id: 'meal2',
            name: 'Greek Yogurt with Berries and Honey',
            prep: '5 min',
            difficulty: 'Easy',
            calories: 220
          });
        }

        // Add more variety based on dietary preferences
        if (profile.dietaryPreferences.includes('vegetarian')) {
          suggestions.push({
            id: 'meal3',
            name: 'Quinoa Bowl with Roasted Vegetables',
            prep: '20 min',
            difficulty: 'Easy',
            calories: 380
          });
        } else {
          suggestions.push({
            id: 'meal4',
            name: 'Chicken and Vegetable Stir Fry',
            prep: '15 min',
            difficulty: 'Easy',
            calories: 320
          });
        }

        setMealSuggestions(suggestions);
        setLoadingSuggestions(false);

        // Save suggestions to AsyncStorage for persistence
        AsyncStorage.setItem('mealSuggestions', JSON.stringify(suggestions))
          .catch(err => console.error('Error saving meal suggestions:', err));
      }, 1500); // Simulate API delay
    } catch (error) {
      console.error('Error generating meal suggestions:', error);
      setLoadingSuggestions(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with Image */}
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        }}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={Colors.brand}
            colors={[Colors.brand]}
          />
        }
      >
        {/* Modern Minimal Header - Shadcn x Apple Philosophy */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.modernHeader}>
          <View style={styles.headerRow}>
            <View style={styles.headerLeft}>
              <Text style={styles.modernGreeting}>Good {getGreeting()}</Text>
              <Text style={styles.modernDate}>{currentTime.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</Text>
            </View>

            <TouchableOpacity
              style={styles.modernAvatar}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                (navigation as any).navigate('Profile');
              }}
            >
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.avatarImage} />
              ) : (
                <Text style={styles.modernAvatarText}>
                  {userProfile.name.split(' ').map(n => n[0]).join('')}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Modern Search Field - Shadcn Style */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.searchContainer}>
          <View style={styles.searchField}>
            <LottieIcon
              name="scan"
              size={20}
              color={Colors.mutedForeground}
              enableHaptics={false}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search recipes, ingredients, or ask AI..."
              placeholderTextColor={Colors.mutedForeground}
              onFocus={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                // Navigate to search/ask screen
                (navigation as any).navigate('Ask');
              }}
            />
          </View>
        </Animated.View>

        {/* Health Monitor - Heart Rate & Steps */}
        <HealthMonitor />

        {/* Modern Nutrition Overview */}
        <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.nutritionCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nutrition Summary</Text>
            <TouchableOpacity style={styles.sectionAction}>
              <Text style={styles.sectionActionText}>Details</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.nutritionGrid}>
            {nutritionData.map((item, index) => (
              <Animated.View
                key={item.label}
                entering={SlideInLeft.delay(500 + index * 100).duration(600)}
                style={styles.nutritionItem}
              >
                <View style={styles.nutritionHeader}>
                  <View style={[styles.nutritionIconWrapper, { backgroundColor: `${item.color}20` }]}>
                    <LottieIcon
                      name={item.lottieIcon as any}
                      size={16}
                      color={item.color}
                      enableHaptics={false}
                    />
                  </View>
                  <Text style={styles.nutritionLabel}>{item.label}</Text>
                </View>

                <View style={styles.nutritionContent}>
                  <Text style={[styles.nutritionValue, { color: item.color }]}>
                    {item.value.toLocaleString()}
                  </Text>
                  <Text style={styles.nutritionUnit}>{item.unit}</Text>
                </View>

                <View style={styles.nutritionProgressBar}>
                  <View style={styles.nutritionProgressTrack}>
                    <View
                      style={[
                        styles.nutritionProgressFill,
                        {
                          width: `${Math.min((item.value / item.target) * 100, 100)}%`,
                          backgroundColor: item.color
                        }
                      ]}
                    />
                  </View>
                  <Text style={styles.nutritionTarget}>
                    {Math.round((item.value / item.target) * 100)}% of {item.target} {item.unit}
                  </Text>
                </View>
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Health Metrics - 2x2 Grid */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.healthMetricsCard}>
          <Text style={styles.sectionTitle}>Health Metrics</Text>
          <View style={styles.metricsGrid}>
            {/* Water Intake */}
            <Animated.View entering={SlideInLeft.delay(700).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="water" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Water</Text>
              </View>
              <Text style={styles.metricValue}>{dailyData.waterConsumed}/{profile.waterGoal}</Text>
              <Text style={styles.metricUnit}>glasses</Text>
              <View style={styles.waterDots}>
                {[...Array(profile.waterGoal)].map((_, i) => (
                  <Animated.View
                    key={i}
                    style={[
                      styles.waterDot,
                      waterWaveStyle,
                      { backgroundColor: i < dailyData.waterConsumed ? Colors.brand : Colors.gray300 }
                    ]}
                  />
                ))}
              </View>
              <TouchableOpacity style={styles.addWaterBtn} onPress={addWater}>
                <Ionicons name="add" size={16} color="white" />
              </TouchableOpacity>
            </Animated.View>

            {/* Steps Counter */}
            <Animated.View entering={SlideInRight.delay(800).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="walk" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Steps</Text>
              </View>
              <Text style={styles.metricValue}>{dailyData.stepsCompleted.toLocaleString()}</Text>
              <Text style={styles.metricUnit}>/ {profile.stepsGoal.toLocaleString()}</Text>
              <View style={styles.progressBar}>
                <Animated.View
                  entering={SlideInLeft.delay(900).duration(1000)}
                  style={[
                    styles.progressFill,
                    {
                      width: `${getProgressPercentage('steps')}%`,
                      backgroundColor: Colors.brand
                    }
                  ]}
                />
              </View>
            </Animated.View>

            {/* Sleep Quality */}
            <Animated.View entering={SlideInLeft.delay(1000).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="moon" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Sleep</Text>
              </View>
              <Text style={styles.metricValue}>7.5h</Text>
              <Text style={styles.metricUnit}>/ 8h</Text>
              <View style={styles.progressBar}>
                <Animated.View
                  entering={SlideInLeft.delay(1100).duration(1000)}
                  style={[
                    styles.progressFill,
                    {
                      width: '94%',
                      backgroundColor: Colors.brand
                    }
                  ]}
                />
              </View>
            </Animated.View>

            {/* Heart Rate */}
            <Animated.View entering={SlideInRight.delay(1200).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="heart" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Heart Rate</Text>
              </View>
              <Text style={styles.metricValue}>72</Text>
              <Text style={styles.metricUnit}>bpm</Text>
              <View style={styles.heartRateIndicator}>
                <Text style={styles.heartRateStatus}>Normal</Text>
              </View>
            </Animated.View>
          </View>
        </Animated.View>

        {/* Recent Meals */}
        <Animated.View entering={FadeInUp.delay(800).duration(600)} style={styles.recentMealsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Meals</Text>
            <TouchableOpacity
              style={styles.sectionAction}
              onPress={() => {
                (navigation as any).navigate('Recipes');
              }}
            >
              <Text style={styles.sectionActionText}>Add New</Text>
            </TouchableOpacity>
          </View>

          {recentMeals.length > 0 ? (
            recentMeals.map((meal, index) => (
              <Animated.View
                key={meal.id}
                entering={SlideInRight.delay(900 + index * 100).duration(500)}
                style={styles.mealItem}
              >
                <View style={[styles.mealIcon, { backgroundColor:
                  meal.type === 'breakfast' ? '#4CAF50' + '20' :
                  meal.type === 'lunch' ? Colors.brand + '20' :
                  meal.type === 'dinner' ? '#FF6B35' + '20' :
                  '#FFC107' + '20'
                }]}>
                  <Ionicons
                    name={meal.icon as any}
                    size={20}
                    color={
                      meal.type === 'breakfast' ? '#4CAF50' :
                      meal.type === 'lunch' ? Colors.brand :
                      meal.type === 'dinner' ? '#FF6B35' :
                      '#FFC107'
                    }
                  />
                </View>
                <View style={styles.mealInfo}>
                  <Text style={styles.mealName}>{meal.name}</Text>
                  <Text style={styles.mealTime}>{meal.time} • {meal.type.charAt(0).toUpperCase() + meal.type.slice(1)}</Text>
                </View>
                <Text style={styles.mealCalories}>{meal.calories > 0 ? `${meal.calories} cal` : ''}</Text>
              </Animated.View>
            ))
          ) : (
            <View style={styles.emptyMealsState}>
              <Ionicons name="restaurant-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptyMealsText}>No recent meals</Text>
              <Text style={styles.emptyMealsSubtext}>Your recently added meals will appear here</Text>
            </View>
          )}
        </Animated.View>

        {/* AI Meal Suggestions */}
        <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={styles.suggestionsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>AI Meal Suggestions</Text>
            <TouchableOpacity
              style={styles.sectionAction}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                generateMealSuggestions();
              }}
            >
              <Ionicons
                name="refresh"
                size={16}
                color={Colors.brand}
                style={loadingSuggestions ? styles.spinningIcon : undefined}
              />
              <Text style={styles.sectionActionText}>Refresh</Text>
            </TouchableOpacity>
          </View>

          {loadingSuggestions ? (
            <View style={styles.loadingContainer}>
              <LottieIcon name="loading" size={48} color={Colors.brand} enableHaptics={false} />
              <Text style={styles.loadingText}>Generating suggestions...</Text>
            </View>
          ) : mealSuggestions.length > 0 ? (
            mealSuggestions.map((suggestion, index) => (
              <Animated.View
                key={suggestion.id}
                entering={BounceIn.delay(1100 + index * 100).duration(600)}
                style={styles.suggestionItem}
              >
                <View style={styles.suggestionInfo}>
                  <Text style={styles.suggestionName}>{suggestion.name}</Text>
                  <View style={styles.suggestionMeta}>
                    <Text style={styles.suggestionDetail}>{suggestion.prep}</Text>
                    <Text style={styles.suggestionDot}>•</Text>
                    <Text style={styles.suggestionDetail}>{suggestion.difficulty}</Text>
                    <Text style={styles.suggestionDot}>•</Text>
                    <Text style={styles.suggestionDetail}>{suggestion.calories} cal</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.suggestionBtn}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                    (navigation as any).navigate('RecipeDetail', { recipeName: suggestion.name });
                  }}
                >
                  <Ionicons name="arrow-forward" size={18} color={Colors.brand} />
                </TouchableOpacity>
              </Animated.View>
            ))
          ) : (
            <View style={styles.emptySuggestionsState}>
              <Ionicons name="bulb-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptySuggestionsText}>
                {!profile.isProfileComplete
                  ? "Complete your profile for personalized suggestions"
                  : "All caught up! You're meeting your goals today."
                }
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Bottom Spacing for Tab Bar */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.solidLight,
  },

  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },

  // Card Base Style
  cardBase: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Header Card
  headerCard: {
    marginTop: 20, // Reduced for mobile app
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Quick Actions Card
  quickActionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Modern Nutrition Card
  nutritionCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 24,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Section Header
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionAction: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: Colors.brandMuted,
  },
  sectionActionText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.brand,
  },

  // Section Title
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.foreground,
    marginBottom: 20,
    letterSpacing: -0.3,
  },

  // Header Section
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  greetingSection: {
    flex: 1,
  },
  greetingText: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  nameText: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.brand,
    letterSpacing: 0.2,
  },
  profileAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brand,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },

  // Progress Bar
  progressBar: {
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    overflow: 'hidden',
    marginTop: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },

  // Modern Nutrition Grid - 2x2 Layout
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    width: '48%',
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: 16,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  nutritionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  nutritionIconWrapper: {
    width: 28,
    height: 28,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  nutritionLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.foreground,
  },
  nutritionContent: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12,
  },
  nutritionValue: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: -0.5,
    marginRight: 4,
  },
  nutritionUnit: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  nutritionProgressBar: {
    marginTop: 4,
  },
  nutritionProgressTrack: {
    width: '100%',
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: 2,
    marginBottom: 6,
  },
  nutritionProgressFill: {
    height: '100%',
    borderRadius: 2,
  },
  nutritionTarget: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.mutedForeground,
    textAlign: 'right',
  },

  // Actions Grid - 2x2 Layout
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionItem: {
    width: '48%', // Proper 2x2 grid
    marginBottom: 16,
  },
  actionButton: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
    textAlign: 'center',
  },

  // Health Metrics Row
  metricsRow: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    gap: 16,
  },

  // Water Card
  waterCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  stepsCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Health Metrics Card - 2x2 Grid
  healthMetricsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  metricGridItem: {
    width: '48%', // Proper 2x2 grid
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    marginBottom: 16,
  },
  heartRateIndicator: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginTop: 8,
  },
  heartRateStatus: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4a5568',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    letterSpacing: -0.5,
  },
  metricUnit: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 12,
  },
  waterDots: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  waterDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  addWaterBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4FC3F7',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },

  // Recent Meals Card
  recentMealsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 24,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  mealItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 8,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  mealIcon: {
    width: 44,
    height: 44,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: 4,
  },
  mealTime: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  mealCalories: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.brand,
  },

  // Suggestions Card
  suggestionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 24,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  spinningIcon: {
    transform: [{ rotate: '45deg' }],
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 8,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: 4,
  },
  suggestionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionDetail: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  suggestionDot: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginHorizontal: 6,
  },
  suggestionBtn: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Modern Minimal Header Styles (Shadcn x Apple)
  modernHeader: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 32,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  modernGreeting: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  modernDate: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    letterSpacing: -0.2,
  },
  modernAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brandShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  modernAvatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.brandForeground,
    letterSpacing: 0.5,
  },

  // Modern Search Field Styles
  searchContainer: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  searchField: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.shadowSm,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    color: Colors.foreground,
    letterSpacing: -0.2,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },

  // Empty states
  emptyMealsState: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  emptyMealsText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginTop: 12,
  },
  emptyMealsSubtext: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: 4,
  },
  emptySuggestionsState: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  emptySuggestionsText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginTop: 12,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});

export default HomeScreenModern;
