import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  ImageBackground,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  BounceIn,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

import { userProfile } from '../constants/UserData';
import { useProfile } from '../contexts/ProfileContext';
import { Colors, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress, NutritionProgress } from '../components/CircularProgress';
import HealthMonitor from '../components/HealthMonitor';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';

const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const { profile, dailyData, incrementWater, getProgressPercentage } = useProfile();
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Animated values for micro-interactions
  const pulseValue = useSharedValue(1);
  const waterWaveValue = useSharedValue(0);

  useEffect(() => {
    // Update time every minute
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);

    // Pulse animation for active elements
    pulseValue.value = withRepeat(
      withSequence(
        withSpring(1.05, { duration: 1000 }),
        withSpring(1, { duration: 1000 })
      ),
      -1,
      true
    );

    // Water wave animation
    waterWaveValue.value = withRepeat(
      withSequence(
        withSpring(1, { duration: 2000 }),
        withSpring(0.8, { duration: 2000 })
      ),
      -1,
      true
    );

    return () => clearInterval(timer);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1200);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const addWater = () => {
    incrementWater();
  };



  const waterWaveStyle = useAnimatedStyle(() => ({
    transform: [{ scale: waterWaveValue.value }],
  }));

  const quickActions = [
    {
      icon: 'scan' as const,
      lottieIcon: 'scanner',
      label: 'Smart Scan',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Scanner');
      },
    },
    {
      icon: 'restaurant' as const,
      lottieIcon: 'recipes',
      label: 'AI Recipes',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Recipes');
      },
    },
    {
      icon: 'calendar' as const,
      lottieIcon: 'plan',
      label: 'Meal Plan',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Plan');
      },
    },
    {
      icon: 'chatbubble-ellipses' as const,
      lottieIcon: 'aiThinking',
      label: 'Ask AI',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Ask');
      },
    },
  ];

  const nutritionData = [
    {
      label: 'Calories',
      value: dailyData.caloriesConsumed,
      target: profile.caloriesGoal,
      unit: 'kcal',
      icon: 'flame',
      lottieIcon: 'fire',
      color: '#FF6B35',
      bgColor: 'rgba(255, 107, 53, 0.1)',
    },
    {
      label: 'Protein',
      value: dailyData.proteinConsumed,
      target: profile.proteinGoal,
      unit: 'g',
      icon: 'fitness',
      lottieIcon: 'heartbeat',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
    },
    {
      label: 'Carbs',
      value: Math.round((dailyData.caloriesConsumed * profile.macroTargets.carbs / 100) / 4),
      target: Math.round((profile.caloriesGoal * profile.macroTargets.carbs / 100) / 4),
      unit: 'g',
      icon: 'leaf',
      lottieIcon: 'broccoli',
      color: '#4CAF50',
      bgColor: 'rgba(76, 175, 80, 0.1)',
    },
    {
      label: 'Fat',
      value: Math.round((dailyData.caloriesConsumed * profile.macroTargets.fat / 100) / 9),
      target: Math.round((profile.caloriesGoal * profile.macroTargets.fat / 100) / 9),
      unit: 'g',
      icon: 'water',
      lottieIcon: 'avocado',
      color: '#FFC107',
      bgColor: 'rgba(255, 193, 7, 0.1)',
    },
  ];

  // Get real recent meals from user's logged meals
  const recentMeals = dailyData.mealsLogged.map((mealName, index) => ({
    name: mealName,
    time: index === 0 ? '8:30 AM' : index === 1 ? '1:15 PM' : '7:00 PM', // Approximate times
    calories: 0, // Would be calculated from actual meal data
    icon: index === 0 ? 'restaurant' : index === 1 ? 'leaf' : 'fitness'
  }));

  // Dynamic meal suggestions based on user preferences and goals
  const getMealSuggestions = () => {
    if (!profile.isProfileComplete) {
      return []; // No suggestions for incomplete profiles
    }

    const suggestions = [];
    const remainingCalories = profile.caloriesGoal - dailyData.caloriesConsumed;

    if (remainingCalories > 300) {
      suggestions.push({
        name: 'Healthy Dinner Option',
        prep: '25 min',
        difficulty: 'Easy',
        calories: Math.min(remainingCalories, 500)
      });
    }

    if (dailyData.proteinConsumed < profile.proteinGoal) {
      suggestions.push({
        name: 'Protein-Rich Snack',
        prep: '10 min',
        difficulty: 'Easy',
        calories: Math.min(remainingCalories, 200)
      });
    }

    return suggestions;
  };

  const mealSuggestions = getMealSuggestions();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with Image */}
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        }}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={Colors.brand}
            colors={[Colors.brand]}
          />
        }
      >
        {/* Modern Minimal Header - Shadcn x Apple Philosophy */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.modernHeader}>
          <View style={styles.headerRow}>
            <View style={styles.headerLeft}>
              <Text style={styles.modernGreeting}>Good {getGreeting()}</Text>
              <Text style={styles.modernDate}>{currentTime.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</Text>
            </View>

            <TouchableOpacity
              style={styles.modernAvatar}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                (navigation as any).navigate('Profile');
              }}
            >
              <Text style={styles.modernAvatarText}>
                {userProfile.name.split(' ').map(n => n[0]).join('')}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Modern Search Field - Shadcn Style */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.searchContainer}>
          <View style={styles.searchField}>
            <LottieIcon
              name="scan"
              size={20}
              color={Colors.mutedForeground}
              enableHaptics={false}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search recipes, ingredients, or ask AI..."
              placeholderTextColor={Colors.mutedForeground}
              onFocus={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                // Navigate to search/ask screen
                (navigation as any).navigate('Ask');
              }}
            />
          </View>
        </Animated.View>

        {/* Health Monitor - Heart Rate & Steps */}
        <HealthMonitor />

        {/* Nutrition Overview - 2x2 Grid */}
        <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.nutritionCard}>
          <Text style={styles.sectionTitle}>Today's Nutrition</Text>
          <View style={styles.nutritionGrid}>
            {nutritionData.map((item, index) => (
              <Animated.View
                key={item.label}
                entering={SlideInLeft.delay(500 + index * 100).duration(600)}
                style={styles.nutritionItem}
              >
                <NutritionProgress
                  label={item.label}
                  value={item.value}
                  target={item.target}
                  unit={item.unit}
                  color={item.color}
                  backgroundColor={item.bgColor}
                  size={65}
                  icon={
                    <LottieIcon
                      name={item.lottieIcon as any}
                      size={20}
                      color={item.color}
                      enableHaptics={false}
                    />
                  }
                />
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Health Metrics - 2x2 Grid */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.healthMetricsCard}>
          <Text style={styles.sectionTitle}>Health Metrics</Text>
          <View style={styles.metricsGrid}>
            {/* Water Intake */}
            <Animated.View entering={SlideInLeft.delay(700).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="water" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Water</Text>
              </View>
              <Text style={styles.metricValue}>{dailyData.waterConsumed}/{profile.waterGoal}</Text>
              <Text style={styles.metricUnit}>glasses</Text>
              <View style={styles.waterDots}>
                {[...Array(profile.waterGoal)].map((_, i) => (
                  <Animated.View
                    key={i}
                    style={[
                      styles.waterDot,
                      waterWaveStyle,
                      { backgroundColor: i < dailyData.waterConsumed ? Colors.brand : Colors.gray300 }
                    ]}
                  />
                ))}
              </View>
              <TouchableOpacity style={styles.addWaterBtn} onPress={addWater}>
                <Ionicons name="add" size={16} color="white" />
              </TouchableOpacity>
            </Animated.View>

            {/* Steps Counter */}
            <Animated.View entering={SlideInRight.delay(800).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="walk" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Steps</Text>
              </View>
              <Text style={styles.metricValue}>{dailyData.stepsCompleted.toLocaleString()}</Text>
              <Text style={styles.metricUnit}>/ {profile.stepsGoal.toLocaleString()}</Text>
              <View style={styles.progressBar}>
                <Animated.View
                  entering={SlideInLeft.delay(900).duration(1000)}
                  style={[
                    styles.progressFill,
                    {
                      width: `${getProgressPercentage('steps')}%`,
                      backgroundColor: Colors.brand
                    }
                  ]}
                />
              </View>
            </Animated.View>

            {/* Sleep Quality */}
            <Animated.View entering={SlideInLeft.delay(1000).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="moon" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Sleep</Text>
              </View>
              <Text style={styles.metricValue}>7.5h</Text>
              <Text style={styles.metricUnit}>/ 8h</Text>
              <View style={styles.progressBar}>
                <Animated.View
                  entering={SlideInLeft.delay(1100).duration(1000)}
                  style={[
                    styles.progressFill,
                    {
                      width: '94%',
                      backgroundColor: Colors.brand
                    }
                  ]}
                />
              </View>
            </Animated.View>

            {/* Heart Rate */}
            <Animated.View entering={SlideInRight.delay(1200).duration(600)} style={styles.metricGridItem}>
              <View style={styles.metricHeader}>
                <Ionicons name="heart" size={20} color={Colors.brand} />
                <Text style={styles.metricTitle}>Heart Rate</Text>
              </View>
              <Text style={styles.metricValue}>72</Text>
              <Text style={styles.metricUnit}>bpm</Text>
              <View style={styles.heartRateIndicator}>
                <Text style={styles.heartRateStatus}>Normal</Text>
              </View>
            </Animated.View>
          </View>
        </Animated.View>

        {/* Recent Meals */}
        <Animated.View entering={FadeInUp.delay(800).duration(600)} style={styles.recentMealsCard}>
          <Text style={styles.sectionTitle}>Recent Meals</Text>
          {recentMeals.length > 0 ? (
            recentMeals.map((meal, index) => (
              <Animated.View
                key={index}
                entering={SlideInRight.delay(900 + index * 100).duration(500)}
                style={styles.mealItem}
              >
                <View style={styles.mealIcon}>
                  <Ionicons name={meal.icon as any} size={20} color={Colors.brand} />
                </View>
                <View style={styles.mealInfo}>
                  <Text style={styles.mealName}>{meal.name}</Text>
                  <Text style={styles.mealTime}>{meal.time}</Text>
                </View>
                <Text style={styles.mealCalories}>{meal.calories > 0 ? `${meal.calories} cal` : ''}</Text>
              </Animated.View>
            ))
          ) : (
            <View style={styles.emptyMealsState}>
              <Ionicons name="restaurant-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptyMealsText}>No meals logged today</Text>
              <Text style={styles.emptyMealsSubtext}>Start tracking your nutrition journey</Text>
            </View>
          )}
        </Animated.View>

        {/* Meal Suggestions */}
        <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={styles.suggestionsCard}>
          <Text style={styles.sectionTitle}>Meal Suggestions</Text>
          {mealSuggestions.length > 0 ? (
            mealSuggestions.map((suggestion, index) => (
              <Animated.View
                key={index}
                entering={BounceIn.delay(1100 + index * 100).duration(600)}
                style={styles.suggestionItem}
              >
                <View style={styles.suggestionInfo}>
                  <Text style={styles.suggestionName}>{suggestion.name}</Text>
                  <View style={styles.suggestionMeta}>
                    <Text style={styles.suggestionDetail}>{suggestion.prep}</Text>
                    <Text style={styles.suggestionDot}>•</Text>
                    <Text style={styles.suggestionDetail}>{suggestion.difficulty}</Text>
                    <Text style={styles.suggestionDot}>•</Text>
                    <Text style={styles.suggestionDetail}>{suggestion.calories} cal</Text>
                  </View>
                </View>
                <TouchableOpacity style={styles.suggestionBtn}>
                  <Ionicons name="add" size={20} color={Colors.brand} />
                </TouchableOpacity>
              </Animated.View>
            ))
          ) : (
            <View style={styles.emptySuggestionsState}>
              <Ionicons name="bulb-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptySuggestionsText}>
                {!profile.isProfileComplete
                  ? "Complete your profile for personalized suggestions"
                  : "All caught up! You're meeting your goals today."
                }
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Bottom Spacing for Tab Bar */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.glass,
  },

  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },

  // Card Base Style
  cardBase: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.glassStrong,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
  },

  // Header Card
  headerCard: {
    marginTop: 20, // Reduced for mobile app
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.glassStrong,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
  },

  // Quick Actions Card
  quickActionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.glassStrong,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
  },

  // Nutrition Card
  nutritionCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.glassStrong,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
  },

  // Section Title
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.foreground,
    marginBottom: 20,
    letterSpacing: -0.3,
  },

  // Header Section
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  greetingSection: {
    flex: 1,
  },
  greetingText: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  nameText: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.brand,
    letterSpacing: 0.2,
  },
  profileAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brand,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },

  // Progress Bar
  progressBar: {
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    overflow: 'hidden',
    marginTop: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },

  // Nutrition Grid - 2x2 Layout
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    width: '48%', // Proper 2x2 grid
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    marginBottom: 16,
  },
  nutritionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  nutritionValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    letterSpacing: -0.5,
  },
  nutritionUnit: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 4,
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4a5568',
    marginBottom: 8,
  },

  // Actions Grid - 2x2 Layout
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionItem: {
    width: '48%', // Proper 2x2 grid
    marginBottom: 16,
  },
  actionButton: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
    textAlign: 'center',
  },

  // Health Metrics Row
  metricsRow: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    gap: 16,
  },

  // Water Card
  waterCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  stepsCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Health Metrics Card - 2x2 Grid
  healthMetricsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  metricGridItem: {
    width: '48%', // Proper 2x2 grid
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    marginBottom: 16,
  },
  heartRateIndicator: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginTop: 8,
  },
  heartRateStatus: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4a5568',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    letterSpacing: -0.5,
  },
  metricUnit: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 12,
  },
  waterDots: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  waterDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  addWaterBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4FC3F7',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },

  // Recent Meals Card
  recentMealsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  mealItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  mealIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 2,
  },
  mealTime: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  mealCalories: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },

  // Suggestions Card
  suggestionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 4,
  },
  suggestionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionDetail: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  suggestionDot: {
    fontSize: 12,
    color: '#6B7280',
    marginHorizontal: 6,
  },
  suggestionBtn: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Modern Minimal Header Styles (Shadcn x Apple)
  modernHeader: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 32,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  modernGreeting: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  modernDate: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    letterSpacing: -0.2,
  },
  modernAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brandShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  modernAvatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.brandForeground,
    letterSpacing: 0.5,
  },

  // Modern Search Field Styles
  searchContainer: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  searchField: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.shadowSm,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    color: Colors.foreground,
    letterSpacing: -0.2,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },

  // Empty states
  emptyMealsState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyMealsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyMealsSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 4,
  },
  emptySuggestionsState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptySuggestionsText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    marginTop: 12,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});

export default HomeScreenModern;
