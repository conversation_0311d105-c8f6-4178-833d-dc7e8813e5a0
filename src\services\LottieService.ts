// Premium Lottie Animation Service for Billion-Dollar App Experience
// All animations use the green color scheme (#6B7C5A variations)

export interface LottieAnimation {
  source: any;
  loop?: boolean;
  autoPlay?: boolean;
  speed?: number;
  colorFilters?: Array<{
    keypath: string;
    color: string;
  }>;
}

// Green-themed Lottie animations for the app
export const LottieAnimations = {
  // Navigation Icons (Green Theme) - Using fallback for now
  home: {
    source: null, // require('../../assets/lottie/home-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.2,
  },
  scanner: {
    source: null, // require('../../assets/lottie/camera-scan-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },
  recipes: {
    source: null, // require('../../assets/lottie/chef-hat-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.1,
  },
  plan: {
    source: null, // require('../../assets/lottie/calendar-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },
  profile: {
    source: null, // require('../../assets/lottie/user-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },

  // Food & Nutrition Icons (Green Theme) - Using fallback for now
  apple: {
    source: null, // require('../../assets/lottie/apple-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },
  carrot: {
    source: null, // require('../../assets/lottie/carrot-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },
  avocado: {
    source: null, // require('../../assets/lottie/avocado-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },
  broccoli: {
    source: null, // require('../../assets/lottie/broccoli-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },

  // Action Icons (Green Theme) - Using fallback for now
  scan: {
    source: null, // require('../../assets/lottie/scan-pulse-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.5,
  },
  loading: {
    source: null, // require('../../assets/lottie/loading-dots-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.2,
  },
  success: {
    source: null, // require('../../assets/lottie/checkmark-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },
  error: {
    source: null, // require('../../assets/lottie/error-x-red.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },

  // Progress & Status (Green Theme) - Using fallback for now
  progressCircle: {
    source: null, // require('../../assets/lottie/progress-circle-green.json'),
    loop: false,
    autoPlay: false,
    speed: 1.0,
  },
  heartbeat: {
    source: null, // require('../../assets/lottie/heartbeat-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },
  water: {
    source: null, // require('../../assets/lottie/water-drop-green.json'),
    loop: false,
    autoPlay: true,
    speed: 1.0,
  },
  fire: {
    source: null, // require('../../assets/lottie/fire-calories-orange.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },

  // Micro-interactions (Green Theme) - Using fallback for now
  buttonPress: {
    source: null, // require('../../assets/lottie/button-press-green.json'),
    loop: false,
    autoPlay: false,
    speed: 1.5,
  },
  cardTap: {
    source: null, // require('../../assets/lottie/card-tap-green.json'),
    loop: false,
    autoPlay: false,
    speed: 1.2,
  },
  swipeUp: {
    source: null, // require('../../assets/lottie/swipe-up-green.json'),
    loop: false,
    autoPlay: false,
    speed: 1.0,
  },

  // Voice & AI (Green Theme) - Using fallback for now
  voiceWave: {
    source: null, // require('../../assets/lottie/voice-wave-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },
  aiThinking: {
    source: null, // require('../../assets/lottie/ai-brain-green.json'),
    loop: true,
    autoPlay: true,
    speed: 0.8,
  },
  typing: {
    source: null, // require('../../assets/lottie/typing-dots-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },

  // Timer & Cooking (Green Theme) - Using fallback for now
  timer: {
    source: null, // require('../../assets/lottie/timer-clock-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },
  cooking: {
    source: null, // require('../../assets/lottie/cooking-pot-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },
  chef: {
    source: null, // require('../../assets/lottie/chef-cooking-green.json'),
    loop: true,
    autoPlay: true,
    speed: 1.0,
  },
};

// Fallback static icons (when Lottie files are not available)
export const FallbackIcons = {
  home: 'home',
  scanner: 'camera',
  recipes: 'restaurant',
  plan: 'calendar',
  profile: 'person',
  apple: 'nutrition',
  carrot: 'leaf',
  avocado: 'ellipse',
  broccoli: 'flower',
  scan: 'scan',
  loading: 'refresh',
  success: 'checkmark-circle',
  error: 'close-circle',
  progressCircle: 'radio-button-on',
  heartbeat: 'heart',
  water: 'water',
  fire: 'flame',
  buttonPress: 'radio-button-on',
  cardTap: 'square',
  swipeUp: 'arrow-up',
  voiceWave: 'mic',
  aiThinking: 'bulb',
  typing: 'ellipsis-horizontal',
  timer: 'time',
  cooking: 'restaurant',
  chef: 'person',
};

class LottieService {
  // Check if Lottie animation file exists
  static hasAnimation(animationKey: keyof typeof LottieAnimations): boolean {
    try {
      const animation = LottieAnimations[animationKey];
      return animation && animation.source !== null;
    } catch (error) {
      console.warn(`Lottie animation '${animationKey}' not found, using fallback icon`);
      return false;
    }
  }

  // Get animation configuration
  static getAnimation(animationKey: keyof typeof LottieAnimations): LottieAnimation | null {
    if (this.hasAnimation(animationKey)) {
      return LottieAnimations[animationKey];
    }
    return null;
  }

  // Get fallback icon name
  static getFallbackIcon(animationKey: keyof typeof LottieAnimations): string {
    return FallbackIcons[animationKey] || 'help-circle';
  }

  // Apply green color filter to animation
  static applyGreenColorFilter(baseColor: string = '#6B7C5A'): Array<{ keypath: string; color: string }> {
    return [
      { keypath: '**', color: baseColor },
      { keypath: '**.Fill 1', color: baseColor },
      { keypath: '**.Stroke 1', color: baseColor },
    ];
  }
}

export default LottieService;
