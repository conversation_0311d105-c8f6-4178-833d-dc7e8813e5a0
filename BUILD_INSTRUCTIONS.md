# EAS Development Build Instructions

This app now uses **EAS Development Build** to access native health sensors for real heart rate and steps monitoring.

## Prerequisites

1. Install EAS CLI globally:
```bash
npm install -g @expo/eas-cli
```

2. Login to your Expo account:
```bash
eas login
```

## Building the Development Client

### For Android (APK)

1. Build the development client:
```bash
eas build --profile development --platform android
```

2. Download and install the APK on your Android device

3. Start the development server:
```bash
npx expo start --dev-client
```

4. Scan the QR code with your development build app

### For iOS (Simulator/Device)

1. Build for iOS:
```bash
eas build --profile development --platform ios
```

2. For device: Download and install via TestFlight or direct installation
3. For simulator: Download and drag to simulator

## Health Monitoring Features

### Real Sensors (Development Build)
- **Steps**: Uses device pedometer for accurate step counting
- **Heart Rate**: Simulated with realistic patterns (requires special hardware for real readings)
- **Permissions**: Automatically requests motion and health permissions

### Fallback Behavior
- If sensors are unavailable, automatically falls back to simulation
- Graceful error handling ensures app continues to work

## Permissions Required

### iOS
- `NSMotionUsageDescription`: Motion sensors for step tracking
- `NSHealthShareUsageDescription`: Health data access
- `NSHealthUpdateUsageDescription`: Health data updates

### Android
- `android.permission.ACTIVITY_RECOGNITION`: Step counting
- `android.permission.BODY_SENSORS`: Health sensors
- `com.google.android.gms.permission.ACTIVITY_RECOGNITION`: Google Fit integration

## Development Workflow

1. **First Time Setup**:
   ```bash
   eas build --profile development --platform android
   # Install the APK on your device
   ```

2. **Daily Development**:
   ```bash
   npx expo start --dev-client
   # Scan QR code with development build
   ```

3. **When Adding New Native Dependencies**:
   ```bash
   eas build --profile development --platform android
   # Rebuild and reinstall
   ```

## Health Service Features

### Real-time Monitoring
- ✅ Live step counting using device pedometer
- ✅ Realistic heart rate simulation (60-100 BPM)
- ✅ Automatic data persistence
- ✅ Haptic feedback integration

### Data Storage
- ✅ Local storage with AsyncStorage
- ✅ Historical data tracking (7 days)
- ✅ Daily averages and progress
- ✅ Health status indicators

### UI Components
- ✅ Circular progress indicators
- ✅ Real-time animations
- ✅ Heart beat pulse effects
- ✅ Modern glassmorphism design

## Testing

### On Device
1. Install development build
2. Enable location services
3. Grant motion permissions
4. Walk around to test step counting
5. Monitor real-time updates in app

### Verification
- Steps should increment as you walk
- Heart rate should show realistic values (60-100 BPM)
- Data should persist between app restarts
- Animations should be smooth (60fps)

## Production Build

When ready for production:

```bash
# Preview build (for testing)
eas build --profile preview --platform android

# Production build
eas build --profile production --platform android
```

## Troubleshooting

### Common Issues

1. **Sensors not working**: Check device permissions in Settings
2. **Build fails**: Ensure all dependencies are compatible
3. **QR code not working**: Use development build, not Expo Go
4. **Steps not counting**: Enable motion & fitness permissions

### Debug Commands

```bash
# Check build status
eas build:list

# View build logs
eas build:view [BUILD_ID]

# Clear cache
npx expo start --clear
```

## Next Steps

1. Build the development client
2. Install on your device
3. Test real step counting
4. Verify health monitoring features
5. Ready for production deployment!
