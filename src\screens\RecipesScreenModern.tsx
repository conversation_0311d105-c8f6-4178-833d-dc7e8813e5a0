import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
  FlatList,
  ImageBackground,
  Pressable,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInRight,
  SlideInLeft,
  ZoomIn,
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  withRepeat,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernInput } from '../components/ModernInput';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress } from '../components/CircularProgress';
import * as Haptics from 'expo-haptics';
import ApiService from '../services/ApiService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

const { width, height } = Dimensions.get('window');

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  image?: string;
  imageUrl?: string; // Unsplash image URL
  rating?: number;
  servings?: number;
  prepTime?: string;
  nutrition?: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

interface FilterChipProps {
  label: string;
  icon?: keyof typeof Ionicons.glyphMap;
  selected: boolean;
  onPress: () => void;
  variant?: 'default' | 'dietary' | 'difficulty' | 'time';
}

interface RecipeCardProps {
  recipe: Recipe;
  onPress: () => void;
  index: number;
  variant?: 'default' | 'featured' | 'compact';
}

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (text: string) => void;
  onVoicePress: () => void;
  onFilterPress: () => void;
  isListening: boolean;
  voiceText: string;
}



// Modern Search Header Component
const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchQuery,
  onSearchChange,
  onVoicePress,
  onFilterPress,
  isListening,
  voiceText,
}) => {
  const voiceScale = useSharedValue(1);
  const voiceOpacity = useSharedValue(1);

  useEffect(() => {
    if (isListening) {
      voiceScale.value = withSequence(
        withTiming(1.2, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );
      voiceOpacity.value = withSequence(
        withTiming(0.7, { duration: 500 }),
        withTiming(1, { duration: 500 })
      );
    }
  }, [isListening]);

  const voiceAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: voiceScale.value }],
    opacity: voiceOpacity.value,
  }));

  return (
    <Animated.View entering={FadeInDown.duration(600)} style={styles.searchHeader}>
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={Colors.brand} style={styles.searchIcon} />
          <TextInput
            value={searchQuery}
            onChangeText={onSearchChange}
            placeholder="Search delicious recipes..."
            placeholderTextColor={Colors.mutedForeground}
            style={styles.searchInput}
          />
          <Animated.View style={voiceAnimatedStyle}>
            <TouchableOpacity
              style={[styles.voiceButton, isListening && styles.voiceButtonActive]}
              onPress={onVoicePress}
            >
              <Ionicons
                name={isListening ? "mic" : "mic-outline"}
                size={18}
                color={isListening ? "#FFFFFF" : Colors.brand}
              />
            </TouchableOpacity>
          </Animated.View>
        </View>
        {voiceText && (
          <Animated.Text entering={FadeInUp.duration(300)} style={styles.voiceText}>
            {voiceText}
          </Animated.Text>
        )}
      </View>

      <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>
        <Ionicons name="options" size={20} color="#6B7C5A" />
      </TouchableOpacity>
    </Animated.View>
  );
};

// Filter Chip Component
const FilterChip: React.FC<FilterChipProps> = ({
  label,
  icon,
  selected,
  onPress,
  variant = 'default'
}) => {
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(selected ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    backgroundColor.value = withTiming(selected ? 0 : 1, { duration: 200 });
    onPress();
  };

  return (
    <Animated.View style={[styles.filterChip, animatedStyle]}>
      <TouchableOpacity
        style={[
          styles.filterChipButton,
          selected && styles.filterChipButtonSelected
        ]}
        onPress={handlePress}
      >
        {icon && (
          <Ionicons
            name={icon}
            size={16}
            color={selected ? "#FFFFFF" : "#6B7C5A"}
            style={styles.filterChipIcon}
          />
        )}
        <Text style={[
          styles.filterChipText,
          selected && styles.filterChipTextSelected
        ]}>
          {label}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Recipe Card Component
const RecipeCard: React.FC<RecipeCardProps> = ({
  recipe,
  onPress,
  index,
  variant = 'default'
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.2, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'hard': return '#F44336';
      default: return '#6B7C5A';
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'checkmark-circle';
      case 'medium': return 'time';
      case 'hard': return 'flame';
      default: return 'help-circle';
    }
  };

  if (variant === 'featured') {
    return (
      <Animated.View
        entering={FadeInUp.delay(index * 100).duration(600)}
        style={[styles.featuredCard, animatedStyle, shadowAnimatedStyle]}
      >
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.featuredCardButton}
          activeOpacity={1}
        >
          <ImageBackground
            source={{ uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80' }}
            style={styles.featuredCardImage}
            imageStyle={styles.featuredCardImageStyle}
          >
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.featuredCardGradient}
            >
              <View style={styles.featuredCardContent}>
                <View style={styles.featuredCardBadges}>
                  <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                    <Ionicons name={getDifficultyIcon(recipe.difficulty) as any} size={12} color="#FFFFFF" />
                    <Text style={styles.difficultyBadgeText}>{recipe.difficulty}</Text>
                  </View>
                  <View style={styles.timeBadge}>
                    <Ionicons name="time" size={12} color="#FFFFFF" />
                    <Text style={styles.timeBadgeText}>{recipe.cookTime}</Text>
                  </View>
                  {recipe.rating && (
                    <View style={styles.ratingBadge}>
                      <Ionicons name="star" size={12} color="#FFD700" />
                      <Text style={styles.ratingBadgeText}>{recipe.rating}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.featuredCardInfo}>
                  <Text style={styles.featuredCardTitle}>{recipe.title}</Text>
                  <Text style={styles.featuredCardDescription} numberOfLines={2}>{recipe.description}</Text>

                  <View style={styles.featuredCardStats}>
                    <View style={styles.statItem}>
                      <Ionicons name="flame" size={16} color="#FF6B6B" />
                      <Text style={styles.statText}>{recipe.calories} cal</Text>
                    </View>
                    {recipe.rating && (
                      <View style={styles.statItem}>
                        <Ionicons name="star" size={16} color={Colors.warning} />
                        <Text style={styles.statText}>{recipe.rating}</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            </LinearGradient>
          </ImageBackground>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      entering={FadeInUp.delay(index * 100).duration(600)}
      style={[styles.recipeCard, animatedStyle, shadowAnimatedStyle]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.recipeCardButton}
        activeOpacity={1}
      >
        <View style={styles.recipeCardImage}>
          <ImageBackground
            source={{ uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80' }}
            style={styles.cardImageBackground}
            imageStyle={styles.cardImageStyle}
          >
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.4)']}
              style={styles.cardImageOverlay}
            >
              <View style={styles.cardBadges}>
                <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                  <Ionicons name={getDifficultyIcon(recipe.difficulty) as any} size={10} color="#FFFFFF" />
                  <Text style={styles.difficultyBadgeText}>{recipe.difficulty}</Text>
                </View>
                {recipe.rating && (
                  <View style={styles.ratingBadge}>
                    <Ionicons name="star" size={10} color="#FFD700" />
                    <Text style={styles.ratingBadgeText}>{recipe.rating}</Text>
                  </View>
                )}
              </View>
            </LinearGradient>
          </ImageBackground>
        </View>

        <View style={styles.recipeCardContent}>
          <Text style={styles.recipeCardTitle} numberOfLines={2}>{recipe.title}</Text>
          <Text style={styles.recipeCardDescription} numberOfLines={2}>
            {recipe.description}
          </Text>

          <View style={styles.recipeCardStats}>
            <View style={styles.statItem}>
              <LottieIcon name="timer" size={14} color={Colors.brand} enableHaptics={false} />
              <Text style={styles.statText}>{recipe.cookTime}</Text>
            </View>
            <View style={styles.statItem}>
              <LottieIcon name="fire" size={14} color="#FF6B35" enableHaptics={false} />
              <Text style={styles.statText}>{recipe.calories} cal</Text>
            </View>
            {recipe.servings && (
              <View style={styles.statItem}>
                <LottieIcon name="chef" size={14} color="#4ECDC4" enableHaptics={false} />
                <Text style={styles.statText}>{recipe.servings}</Text>
              </View>
            )}
          </View>

          {/* Ingredient Preview */}
          {recipe.ingredients && recipe.ingredients.length > 0 && (
            <View style={styles.ingredientPreview}>
              <Text style={styles.ingredientPreviewText}>
                {recipe.ingredients.slice(0, 3).join(', ')}
                {recipe.ingredients.length > 3 && '...'}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const RecipesScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDietary, setSelectedDietary] = useState<string[]>([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [maxCookTime, setMaxCookTime] = useState<string>('');
  const [availableIngredients, setAvailableIngredients] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'relevance' | 'time' | 'calories' | 'rating'>('relevance');

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'gluten-free', label: 'Gluten-Free', icon: 'shield-checkmark' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
  ];

  const difficultyOptions = [
    { id: 'easy', label: 'Easy', icon: 'happy' },
    { id: 'medium', label: 'Medium', icon: 'star' },
    { id: 'hard', label: 'Hard', icon: 'flame' },
  ];

  const timeOptions = [
    { id: '15', label: '15 min' },
    { id: '30', label: '30 min' },
    { id: '45', label: '45 min' },
    { id: '60', label: '1 hour' },
  ];

  // Animation values
  const voiceScale = useSharedValue(1);
  const voiceOpacity = useSharedValue(1);

  const voiceAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: voiceScale.value }],
    opacity: voiceOpacity.value,
  }));

  useEffect(() => {
    if (isListening) {
      voiceScale.value = withSequence(
        withTiming(1.2, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );
      voiceOpacity.value = withSequence(
        withTiming(0.7, { duration: 500 }),
        withTiming(1, { duration: 500 })
      );
    }
  }, [isListening]);

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    const callbacks: VoiceServiceCallbacks = {
      onStart: () => {
        setIsListening(true);
        setVoiceText('Listening...');
      },
      onResult: (result) => {
        setVoiceText('');
        setIsListening(false);
        setSearchQuery(result.text);
      },
      onError: (error) => {
        setIsListening(false);
        setVoiceText('');
        Alert.alert('Voice Error', error);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onPartialResult: (text) => {
        setVoiceText(text);
      },
    };

    const started = await VoiceService.startListening(callbacks);
    if (!started) {
      Alert.alert('Voice Error', 'Unable to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    await VoiceService.stopListening();
    setIsListening(false);
    setVoiceText('');
  };

  const generateRecipes = async () => {
    setLoading(true);
    try {
      // Build enhanced query with filters
      let query = searchQuery || 'healthy meal';

      if (selectedDietary.length > 0) {
        query += ` that is ${selectedDietary.join(' and ')}`;
      }

      if (selectedDifficulty) {
        query += ` with ${selectedDifficulty} difficulty level`;
      }

      if (maxCookTime) {
        query += ` that takes maximum ${maxCookTime} minutes to cook`;
      }

      if (availableIngredients.length > 0) {
        query += ` using ingredients like ${availableIngredients.join(', ')}`;
      }

      const result = await ApiService.generateRecipe(query);

      // Convert single recipe to array format
      const recipeWithId = {
        id: Date.now().toString(),
        title: result.recipeTitle,
        description: `A delicious ${query} recipe with ${result.estimatedCalories} calories`,
        cookTime: '25 min', // Default value
        difficulty: 'Easy' as const,
        calories: result.estimatedCalories,
        ingredients: result.ingredients,
        instructions: result.steps,
        tags: result.tags,
        image: result.imageUrl, // Use the Unsplash image URL
        imageUrl: result.imageUrl // Also set imageUrl for consistency
      };

      setRecipes([recipeWithId]);
    } catch (error) {
      console.error('Error generating recipes:', error);
      // Fallback to mock data
      setRecipes([
        {
          id: '1',
          title: 'Mediterranean Quinoa Bowl',
          description: 'A nutritious bowl packed with fresh vegetables, quinoa, and olive oil dressing',
          cookTime: '25 min',
          difficulty: 'Easy',
          calories: 420,
          ingredients: ['Quinoa', 'Cherry tomatoes', 'Cucumber', 'Feta cheese', 'Olive oil', 'Lemon'],
          instructions: ['Cook quinoa', 'Chop vegetables', 'Mix with dressing', 'Serve fresh'],
          tags: ['Healthy', 'Mediterranean', 'Vegetarian']
        },
        {
          id: '2',
          title: 'Grilled Salmon with Asparagus',
          description: 'Perfectly grilled salmon with roasted asparagus and herbs',
          cookTime: '20 min',
          difficulty: 'Medium',
          calories: 380,
          ingredients: ['Salmon fillet', 'Asparagus', 'Lemon', 'Herbs', 'Olive oil'],
          instructions: ['Season salmon', 'Grill for 6-8 minutes', 'Roast asparagus', 'Serve with lemon'],
          tags: ['High Protein', 'Low Carb', 'Omega-3']
        }
      ]);
    }
    setLoading(false);
  };

  const toggleDietary = (option: string) => {
    setSelectedDietary(prev =>
      prev.includes(option)
        ? prev.filter(item => item !== option)
        : [...prev, option]
    );
  };

  const searchRecipes = () => {
    generateRecipes();
  };

  const getRandomRecipe = () => {
    setSearchQuery('surprise me with a random recipe');
    generateRecipes();
  };

  // Helper functions for recipe cards
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'hard': return '#F44336';
      default: return '#6B7C5A';
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'checkmark-circle';
      case 'medium': return 'time';
      case 'hard': return 'flame';
      default: return 'help-circle';
    }
  };

  const clearFilters = () => {
    setSelectedDietary([]);
    setSelectedDifficulty('');
    setMaxCookTime('');
    setAvailableIngredients([]);
  };

  const toggleDietaryFilter = (option: string) => {
    toggleDietary(option);
  };

  const DietaryChip: React.FC<{ option: any; isSelected: boolean; onPress: () => void }> = ({
    option,
    isSelected,
    onPress
  }) => (
    <TouchableOpacity
      style={[styles.dietaryChip, isSelected && styles.dietaryChipSelected]}
      onPress={onPress}
    >
      <Ionicons
        name={option.icon as any}
        size={16}
        color={isSelected ? Colors.brandForeground : Colors.brand}
      />
      <Text style={[
        styles.dietaryChipText,
        isSelected && styles.dietaryChipTextSelected
      ]}>
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const RecipeCard: React.FC<{ recipe: Recipe; delay: number }> = ({ recipe, delay }) => {
    const cardNavigation = useNavigation();

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
        <TouchableOpacity style={styles.recipeCard}>
          <View style={styles.recipeHeader}>
            <View style={styles.recipeInfo}>
              <Text style={styles.recipeTitle}>{recipe.title}</Text>
              <Text style={styles.recipeDescription}>{recipe.description}</Text>
            </View>
            <View style={styles.recipeStats}>
              <View style={styles.statItem}>
                <Ionicons name="time" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.cookTime}</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons name="flame" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.calories} cal</Text>
              </View>
            </View>
          </View>

          <View style={styles.recipeTags}>
            {recipe.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
            <View style={[styles.tag, styles.difficultyTag]}>
              <Text style={styles.difficultyText}>{recipe.difficulty}</Text>
            </View>
          </View>

          <View style={styles.recipeFooter}>
            <Text style={styles.ingredientsLabel}>
              {recipe.ingredients.length} ingredients
            </Text>
            <TouchableOpacity
              style={styles.viewButton}
              onPress={() => (cardNavigation as any).navigate('RecipeDetail', { recipe })}
            >
              <Text style={styles.viewButtonText}>View Recipe</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.brand} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const featuredRecipes = recipes.slice(0, 3);
  const regularRecipes = recipes.slice(3);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with Subtle Image */}
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        }}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Modern Search Header */}
        <View style={styles.modernSearchHeader}>
          <Animated.View entering={FadeInDown.duration(600)} style={styles.searchSection}>
            <Text style={styles.headerTitle}>Discover Recipes</Text>
            <Text style={styles.headerSubtitle}>Find your perfect meal</Text>

            <View style={styles.modernSearchContainer}>
              <BlurView intensity={25} style={styles.searchInputWrapper}>
                <LottieIcon
                  name="scan"
                  size={20}
                  color={Colors.brand}
                  enableHaptics={false}
                />
                <TextInput
                  value={searchQuery}
                  onChangeText={(text) => {
                    setSearchQuery(text);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  placeholder="Search delicious recipes..."
                  placeholderTextColor={Colors.mutedForeground}
                  style={styles.modernSearchInput}
                />
                <Animated.View style={voiceAnimatedStyle}>
                  <TouchableOpacity
                    style={[styles.modernVoiceButton, isListening && styles.voiceButtonActive]}
                    onPress={() => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                      isListening ? stopVoiceRecognition() : startVoiceRecognition();
                    }}
                  >
                    <LottieIcon
                      name={isListening ? "voiceWave" : "voiceWave"}
                      size={18}
                      color={isListening ? "#FFFFFF" : Colors.brand}
                      enableHaptics={false}
                    />
                  </TouchableOpacity>
                </Animated.View>
              </BlurView>
              {voiceText && (
                <Animated.Text entering={FadeInUp.duration(300)} style={styles.modernVoiceText}>
                  {voiceText}
                </Animated.Text>
              )}
            </View>
          </Animated.View>
        </View>
        {/* Beautiful Filter Chips - All Visible */}
        <Animated.View entering={FadeInLeft.delay(200).duration(600)} style={styles.modernFiltersSection}>
          <View style={styles.modernFiltersGrid}>
            {dietaryOptions.map((option, index) => (
              <Animated.View
                key={option.id}
                entering={ZoomIn.delay(index * 100).duration(500)}
                style={styles.filterChipWrapper}
              >
                <TouchableOpacity
                  style={[
                    styles.modernFilterChip,
                    selectedDietary.includes(option.id) && styles.modernFilterChipSelected
                  ]}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    toggleDietaryFilter(option.id);
                  }}
                >
                  <BlurView
                    intensity={selectedDietary.includes(option.id) ? 0 : 10}
                    style={styles.filterChipBlur}
                  >
                    <LottieIcon
                      name={option.id === 'vegetarian' ? 'broccoli' : option.id === 'vegan' ? 'apple' : option.id === 'gluten-free' ? 'avocado' : option.id === 'keto' ? 'fire' : option.id === 'low-carb' ? 'progressCircle' : 'heartbeat'}
                      size={16}
                      color={selectedDietary.includes(option.id) ? Colors.brandForeground : Colors.brand}
                      enableHaptics={false}
                    />
                    <Text style={[
                      styles.modernFilterText,
                      selectedDietary.includes(option.id) && styles.modernFilterTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </BlurView>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Modern Section Header */}
        <Animated.View entering={FadeInRight.delay(300).duration(600)} style={styles.modernSectionHeader}>
          <Text style={styles.modernSectionTitle}>
            {recipes.length > 0 ? `${recipes.length} Delicious Recipes` : 'Discover Amazing Recipes'}
          </Text>
          <Text style={styles.modernSectionSubtitle}>
            Handpicked recipes just for you
          </Text>
        </Animated.View>

        {/* Beautiful Loading State */}
        {loading && (
          <Animated.View entering={FadeInUp.duration(400)} style={styles.modernLoadingContainer}>
            <View style={styles.loadingCard}>
              <ActivityIndicator size="large" color="#6B7C5A" />
              <Text style={styles.loadingText}>Finding perfect recipes for you...</Text>
            </View>
          </Animated.View>
        )}

        {/* Beautiful Recipe Cards */}
        {recipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernRecipesSection}>
            {recipes.map((recipe, index) => (
              <Animated.View
                key={recipe.id}
                entering={ZoomIn.delay(index * 150).duration(600)}
                style={styles.modernRecipeCard}
              >
                <TouchableOpacity
                  onPress={() => (navigation as any).navigate('RecipeDetail', { recipe })}
                  style={styles.modernRecipeCardButton}
                  activeOpacity={0.95}
                >
                  <ImageBackground
                    source={{
                      uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'
                    }}
                    style={styles.modernRecipeImage}
                    imageStyle={styles.modernRecipeImageStyle}
                  >
                    <LinearGradient
                      colors={['transparent', 'rgba(0,0,0,0.6)']}
                      style={styles.modernRecipeGradient}
                    >
                      <View style={styles.modernRecipeBadges}>
                        <View style={[styles.modernDifficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                          <Ionicons name={getDifficultyIcon(recipe.difficulty) as any} size={12} color="#FFFFFF" />
                          <Text style={styles.modernBadgeText}>{recipe.difficulty}</Text>
                        </View>
                        <View style={styles.modernTimeBadge}>
                          <Ionicons name="time" size={12} color="#FFFFFF" />
                          <Text style={styles.modernBadgeText}>{recipe.cookTime}</Text>
                        </View>
                      </View>
                    </LinearGradient>
                  </ImageBackground>

                  <View style={styles.modernRecipeContent}>
                    <Text style={styles.modernRecipeTitle} numberOfLines={2}>{recipe.title}</Text>
                    <Text style={styles.modernRecipeDescription} numberOfLines={2}>
                      {recipe.description}
                    </Text>

                    <View style={styles.modernRecipeStats}>
                      <View style={styles.modernStatItem}>
                        <Ionicons name="flame" size={16} color="#FF6B6B" />
                        <Text style={styles.modernStatText}>{recipe.calories} cal</Text>
                      </View>
                      {recipe.rating && (
                        <View style={styles.modernStatItem}>
                          <Ionicons name="star" size={16} color="#FFD700" />
                          <Text style={styles.modernStatText}>{recipe.rating}</Text>
                        </View>
                      )}
                      {recipe.servings && (
                        <View style={styles.modernStatItem}>
                          <Ionicons name="people" size={16} color="#4ECDC4" />
                          <Text style={styles.modernStatText}>{recipe.servings}</Text>
                        </View>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </Animated.View>
        )}

        {/* Beautiful Empty State */}
        {!loading && recipes.length === 0 && (
          <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.modernEmptyState}>
            <View style={styles.modernEmptyStateCard}>
              <View style={styles.modernEmptyIcon}>
                <Ionicons name="restaurant" size={48} color="#6B7C5A" />
              </View>
              <Text style={styles.modernEmptyTitle}>No recipes found</Text>
              <Text style={styles.modernEmptyDescription}>
                Try adjusting your search to discover amazing recipes
              </Text>
              <TouchableOpacity
                style={styles.modernEmptyButton}
                onPress={searchRecipes}
              >
                <Text style={styles.modernEmptyButtonText}>Explore Recipes</Text>
                <Ionicons name="arrow-forward" size={16} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(700).duration(600)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <ModernButton
              title="Search Recipes"
              onPress={searchRecipes}
              variant="primary"
              size="md"
              icon="search"
              style={styles.quickActionButton}
            />
            <ModernButton
              title="Random Recipe"
              onPress={getRandomRecipe}
              variant="secondary"
              size="md"
              icon="shuffle"
              style={styles.quickActionButton}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Filters Modal */}
      <ModernModal
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        title="Filter Recipes"
        variant="bottom"
        size="lg"
      >
        <View style={styles.filtersModalContent}>
          <Text style={styles.modalSectionTitle}>Dietary Preferences</Text>
          <View style={styles.modalFiltersGrid}>
            {dietaryOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDietary.includes(option.id)}
                onPress={() => toggleDietaryFilter(option.id)}
                variant="dietary"
              />
            ))}
          </View>

          <Text style={styles.modalSectionTitle}>Difficulty Level</Text>
          <View style={styles.modalFiltersGrid}>
            {difficultyOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDifficulty === option.id}
                onPress={() => setSelectedDifficulty(selectedDifficulty === option.id ? '' : option.id)}
                variant="difficulty"
              />
            ))}
          </View>

          <Text style={styles.modalSectionTitle}>Cooking Time</Text>
          <View style={styles.modalFiltersGrid}>
            {timeOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                selected={maxCookTime === option.id}
                onPress={() => setMaxCookTime(maxCookTime === option.id ? '' : option.id)}
                variant="time"
              />
            ))}
          </View>

          <View style={styles.modalButtons}>
            <ModernButton
              title="Clear All"
              onPress={clearFilters}
              variant="outline"
              size="md"
              style={styles.modalClearButton}
            />
            <ModernButton
              title="Apply Filters"
              onPress={() => {
                setShowFilters(false);
                searchRecipes();
              }}
              variant="primary"
              size="md"
              style={styles.modalApplyButton}
            />
          </View>
        </View>
      </ModernModal>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  // Beautiful Background with Subtle Image
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.92)',
  },

  // Modern Search Header
  modernSearchHeader: {
    paddingTop: 20, // Reduced for mobile app
    paddingHorizontal: 20,
    paddingBottom: 24,
    backgroundColor: 'transparent',
  },
  searchSection: {
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 4,
    letterSpacing: -0.8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
    marginBottom: 24,
    textAlign: 'center',
  },
  modernSearchContainer: {
    width: '100%',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  searchIcon: {
    marginRight: 12,
  },
  modernSearchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#1a202c',
  },
  modernVoiceButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  voiceButtonActive: {
    backgroundColor: '#6B7C5A',
  },
  modernVoiceText: {
    fontSize: 14,
    color: '#6B7C5A',
    fontStyle: 'italic',
    marginTop: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },

  // Modern Filter Chips
  modernFiltersSection: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  modernFiltersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  filterChipWrapper: {
    marginBottom: 8,
  },
  modernFilterChip: {
    borderRadius: 32,
    marginRight: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    overflow: 'hidden',
  },
  filterChipBlur: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  modernFilterChipSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
    shadowColor: '#6B7C5A',
    shadowOpacity: 0.3,
  },
  filterIcon: {
    marginRight: 4,
  },
  modernFilterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
  },
  modernFilterTextSelected: {
    color: '#FFFFFF',
  },

  // Modern Section Header
  modernSectionHeader: {
    paddingHorizontal: 20,
    marginBottom: 24,
    alignItems: 'center',
  },
  modernSectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 4,
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  modernSectionSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Modern Loading State
  modernLoadingContainer: {
    paddingVertical: 60,
    alignItems: 'center',
  },
  loadingCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },

  // Beautiful Modern Recipe Cards
  modernRecipesSection: {
    paddingHorizontal: 20,
  },
  modernRecipeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  modernRecipeCardButton: {
    flex: 1,
  },
  modernRecipeImage: {
    height: 200,
    width: '100%',
  },
  modernRecipeImageStyle: {
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  modernRecipeGradient: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  modernRecipeBadges: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  modernDifficultyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 4,
  },
  modernTimeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    gap: 4,
  },
  modernBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  modernRecipeContent: {
    padding: 24,
  },
  modernRecipeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    letterSpacing: -0.3,
    lineHeight: 26,
  },
  modernRecipeDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
    fontWeight: '500',
  },
  modernRecipeStats: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },
  modernStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  modernStatText: {
    fontSize: 14,
    color: '#4a5568',
    fontWeight: '600',
  },

  // Beautiful Empty State
  modernEmptyState: {
    paddingVertical: 60,
    alignItems: 'center',
  },
  modernEmptyStateCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    maxWidth: 320,
  },
  modernEmptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  modernEmptyTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    letterSpacing: -0.3,
    textAlign: 'center',
  },
  modernEmptyDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 24,
    fontWeight: '500',
    textAlign: 'center',
  },
  modernEmptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#6B7C5A',
    paddingHorizontal: 24,
    paddingVertical: 14,
    borderRadius: 24,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  modernEmptyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // Billion-Dollar Search Header
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 24,
    backgroundColor: '#FAFAFA',
    gap: 16,
  },
  searchContainer: {
    flex: 1,
  },
  searchInput: {
    marginBottom: 0,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontStyle: 'italic',
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Enhanced Search Input
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Voice Button (for SearchHeader component)
  voiceButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },

  // Enhanced Filter Chips
  filtersSection: {
    marginBottom: 24,
  },
  filtersContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  filterChip: {
    borderRadius: 20,
    marginRight: 12,
  },
  filterChipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  filterChipButtonSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  filterChipIcon: {
    marginRight: 4,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
  },
  filterChipTextSelected: {
    color: '#FFFFFF',
  },

  // View Mode Section
  viewModeSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
  },
  viewModeButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  viewModeButtonActive: {
    backgroundColor: Colors.brand,
  },

  // Loading
  loadingContainer: {
    paddingVertical: Spacing.xxxl,
    alignItems: 'center',
  },

  // Enhanced Featured Section
  featuredSection: {
    marginBottom: 32,
  },
  featuredContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  featuredCard: {
    width: width * 0.85,
    height: 280,
    borderRadius: 32,
    overflow: 'hidden',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
  },
  featuredCardButton: {
    flex: 1,
  },
  featuredCardImage: {
    flex: 1,
  },
  featuredCardImageStyle: {
    borderRadius: 32,
  },
  featuredCardGradient: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 24,
  },
  featuredCardContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  featuredCardBadges: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  featuredCardInfo: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  featuredCardTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  featuredCardDescription: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 16,
    fontWeight: '500',
  },
  featuredCardStats: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },

  // Quick Actions Section
  quickActionsSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 16,
  },
  quickActionButton: {
    flex: 1,
  },

  // Recipe Card Styles
  recipeCardButton: {
    flex: 1,
  },
  recipeCardImage: {
    height: 160,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
  },
  cardImageBackground: {
    flex: 1,
  },
  cardImageStyle: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  cardImageOverlay: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 16,
  },
  cardBadges: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'flex-start',
  },
  recipeCardContent: {
    padding: 20,
  },
  recipeCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  recipeCardDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
    marginBottom: 12,
  },
  recipeCardStats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  ingredientPreview: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 12,
    padding: 12,
  },
  ingredientPreviewText: {
    fontSize: 12,
    color: '#6B7C5A',
    fontWeight: '500',
  },

  // Badge Styles
  difficultyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  difficultyBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  timeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    gap: 4,
  },
  timeBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    gap: 4,
  },
  ratingBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFD700',
  },

  // Modal Styles
  filtersModalContent: {
    padding: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 16,
    marginTop: 16,
  },
  modalFiltersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 8,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 32,
  },
  modalClearButton: {
    flex: 1,
  },
  modalApplyButton: {
    flex: 1,
  },

  // Additional Recipe Card Styles
  recipeHeader: {
    padding: 20,
    paddingBottom: 12,
  },
  recipeInfo: {
    marginBottom: 12,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  recipeDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  recipeTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  tag: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: '#6B7C5A',
    fontWeight: '600',
  },
  difficultyTag: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
  },
  difficultyText: {
    color: '#FF9800',
  },
  recipeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  ingredientsLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  viewButtonText: {
    fontSize: 14,
    color: '#6B7C5A',
    fontWeight: '600',
  },

  // Stat Item Styles
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    color: '#4a5568',
    fontWeight: '600',
  },

  // Recipe Card Main Style
  recipeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },

  // Dietary Chip Styles
  dietaryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderWidth: 1,
    borderColor: '#6B7C5A',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    gap: 8,
  },
  dietaryChipSelected: {
    backgroundColor: '#6B7C5A',
  },
  dietaryChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  dietaryChipTextSelected: {
    color: '#FFFFFF',
  },

  // Recipe Stats
  recipeStats: {
    flexDirection: 'row',
    gap: 16,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 100,
  },

});

export default RecipesScreenModern;
