import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Animated, {
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';

import { Colors } from '../constants/Colors';
import { CircularProgress } from './CircularProgress';
import LottieIcon from './LottieIcon';
import HealthService, { HealthData, HeartRateReading, StepsData } from '../services/HealthService';

const { width } = Dimensions.get('window');

interface HealthMonitorProps {
  style?: any;
}

export const HealthMonitor: React.FC<HealthMonitorProps> = ({ style }) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [healthData, setHealthData] = useState<HealthData | null>(null);
  const [heartRateData, setHeartRateData] = useState<HeartRateReading | null>(null);
  const [stepsData, setStepsData] = useState<StepsData | null>(null);

  // Animation values
  const heartBeatScale = useSharedValue(1);
  const pulseOpacity = useSharedValue(0.3);

  useEffect(() => {
    initializeHealth();
    return () => {
      HealthService.stopMonitoring();
    };
  }, []);

  useEffect(() => {
    if (isMonitoring) {
      // Start heart beat animation
      heartBeatScale.value = withRepeat(
        withTiming(1.2, { duration: 600 }),
        -1,
        true
      );
      
      // Start pulse animation
      pulseOpacity.value = withRepeat(
        withTiming(0.8, { duration: 1000 }),
        -1,
        true
      );

      // Update data every 2 seconds
      const interval = setInterval(updateHealthData, 2000);
      return () => clearInterval(interval);
    } else {
      heartBeatScale.value = withSpring(1);
      pulseOpacity.value = withSpring(0.3);
    }
  }, [isMonitoring]);

  const initializeHealth = async () => {
    try {
      const initialized = await HealthService.initializeHealthMonitoring();
      if (initialized) {
        await HealthService.startMonitoring();
        setIsMonitoring(true);
        updateHealthData();
      }
    } catch (error) {
      console.error('Error initializing health monitoring:', error);
    }
  };

  const updateHealthData = () => {
    const health = HealthService.getCurrentHealthData();
    const heartRate = HealthService.getCurrentHeartRate();
    const steps = HealthService.getCurrentSteps();

    setHealthData(health);
    setHeartRateData(heartRate);
    setStepsData(steps);
  };

  const toggleMonitoring = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      if (isMonitoring) {
        await HealthService.stopMonitoring();
        setIsMonitoring(false);
      } else {
        await HealthService.startMonitoring();
        setIsMonitoring(true);
        updateHealthData();
      }
    } catch (error) {
      console.error('Error toggling monitoring:', error);
    }
  };

  const heartBeatAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartBeatScale.value }],
  }));

  const pulseAnimatedStyle = useAnimatedStyle(() => ({
    opacity: pulseOpacity.value,
  }));

  const getHeartRateColor = (bpm: number) => {
    if (bpm < 60) return '#3B82F6'; // Blue for low
    if (bpm > 100) return '#EF4444'; // Red for high
    return '#10B981'; // Green for normal
  };

  const getStepsProgress = (steps: number) => {
    const dailyGoal = 10000;
    return Math.min((steps / dailyGoal) * 100, 100);
  };

  if (!healthData || !heartRateData || !stepsData) {
    return (
      <Animated.View entering={FadeInUp.duration(600)} style={[styles.container, style]}>
        <BlurView intensity={10} style={styles.loadingCard}>
          <LottieIcon name="heartbeat" size={32} color={Colors.brand} enableHaptics={false} />
          <Text style={styles.loadingText}>Initializing Health Monitor...</Text>
        </BlurView>
      </Animated.View>
    );
  }

  return (
    <Animated.View entering={FadeInUp.duration(600)} style={[styles.container, style]}>
      {/* Health Monitor Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Health Monitor</Text>
          <Text style={styles.subtitle}>Real-time tracking</Text>
        </View>
        
        <TouchableOpacity
          style={[styles.toggleButton, isMonitoring && styles.toggleButtonActive]}
          onPress={toggleMonitoring}
        >
          <Text style={[styles.toggleText, isMonitoring && styles.toggleTextActive]}>
            {isMonitoring ? 'ON' : 'OFF'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Health Metrics Grid */}
      <View style={styles.metricsGrid}>
        {/* Heart Rate Card */}
        <View style={styles.metricCard}>
          <BlurView intensity={15} style={styles.cardBlur}>
            <View style={styles.cardContent}>
              <View style={styles.metricHeader}>
                <Animated.View style={[styles.heartIconContainer, heartBeatAnimatedStyle]}>
                  <Animated.View style={[styles.heartPulse, pulseAnimatedStyle]} />
                  <LottieIcon 
                    name="heartbeat" 
                    size={24} 
                    color={getHeartRateColor(heartRateData.bpm)} 
                    enableHaptics={false}
                  />
                </Animated.View>
                <Text style={styles.metricLabel}>Heart Rate</Text>
              </View>
              
              <Text style={[styles.metricValue, { color: getHeartRateColor(heartRateData.bpm) }]}>
                {heartRateData.bpm}
              </Text>
              <Text style={styles.metricUnit}>BPM</Text>
              
              <View style={styles.confidenceBar}>
                <View 
                  style={[
                    styles.confidenceFill, 
                    { 
                      width: `${heartRateData.confidence * 100}%`,
                      backgroundColor: getHeartRateColor(heartRateData.bpm)
                    }
                  ]} 
                />
              </View>
              <Text style={styles.confidenceText}>
                {Math.round(heartRateData.confidence * 100)}% accuracy
              </Text>
            </View>
          </BlurView>
        </View>

        {/* Steps Card */}
        <View style={styles.metricCard}>
          <BlurView intensity={15} style={styles.cardBlur}>
            <View style={styles.cardContent}>
              <View style={styles.metricHeader}>
                <View style={styles.stepsIconContainer}>
                  <LottieIcon 
                    name="progressCircle" 
                    size={24} 
                    color={Colors.brand} 
                    enableHaptics={false}
                  />
                </View>
                <Text style={styles.metricLabel}>Steps</Text>
              </View>
              
              <Text style={[styles.metricValue, { color: Colors.brand }]}>
                {stepsData.steps.toLocaleString()}
              </Text>
              <Text style={styles.metricUnit}>steps</Text>
              
              <CircularProgress
                size={60}
                progress={getStepsProgress(stepsData.steps)}
                color={Colors.brand}
                backgroundColor={Colors.borderLight}
                strokeWidth={4}
                showPercentage={false}
                style={styles.stepsProgress}
              />
              
              <Text style={styles.progressText}>
                {Math.round(getStepsProgress(stepsData.steps))}% of daily goal
              </Text>
            </View>
          </BlurView>
        </View>
      </View>

      {/* Additional Metrics */}
      <View style={styles.additionalMetrics}>
        <View style={styles.additionalMetric}>
          <LottieIcon name="avocado" size={16} color="#F59E0B" enableHaptics={false} />
          <Text style={styles.additionalValue}>{stepsData.calories}</Text>
          <Text style={styles.additionalLabel}>cal</Text>
        </View>
        
        <View style={styles.additionalMetric}>
          <LottieIcon name="progressCircle" size={16} color="#3B82F6" enableHaptics={false} />
          <Text style={styles.additionalValue}>{(stepsData.distance / 1000).toFixed(1)}</Text>
          <Text style={styles.additionalLabel}>km</Text>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  loadingCard: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.3,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 2,
  },
  toggleButton: {
    backgroundColor: Colors.muted,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  toggleButtonActive: {
    backgroundColor: Colors.brand,
    borderColor: Colors.brand,
  },
  toggleText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.mutedForeground,
  },
  toggleTextActive: {
    color: Colors.brandForeground,
  },
  metricsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  metricCard: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
  },
  cardBlur: {
    padding: 16,
  },
  cardContent: {
    alignItems: 'center',
  },
  metricHeader: {
    alignItems: 'center',
    marginBottom: 12,
  },
  heartIconContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  heartPulse: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#10B981',
    top: -8,
    left: -8,
  },
  stepsIconContainer: {
    marginBottom: 8,
  },
  metricLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: -0.5,
    marginBottom: 2,
  },
  metricUnit: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginBottom: 12,
  },
  confidenceBar: {
    width: '100%',
    height: 3,
    backgroundColor: Colors.muted,
    borderRadius: 2,
    marginBottom: 6,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 2,
  },
  confidenceText: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  stepsProgress: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  additionalMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: Colors.muted,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  additionalMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  additionalValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.foreground,
  },
  additionalLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
});

export default HealthMonitor;
