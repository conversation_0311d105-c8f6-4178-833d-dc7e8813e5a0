import React, { ReactNode, useEffect } from 'react';
import { View, ViewStyle, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  interpolate,
  Easing,
  runOnJS,
} from 'react-native-reanimated';
import { AnimationConfig } from '../utils/AnimationUtils';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface FadeTransitionProps {
  visible: boolean;
  children: ReactNode;
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

interface SlideTransitionProps {
  visible: boolean;
  children: ReactNode;
  direction: 'left' | 'right' | 'up' | 'down';
  distance?: number;
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

interface ScaleTransitionProps {
  visible: boolean;
  children: ReactNode;
  fromScale?: number;
  toScale?: number;
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

interface FlipTransitionProps {
  visible: boolean;
  children: ReactNode;
  axis: 'x' | 'y';
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

interface MorphTransitionProps {
  visible: boolean;
  children: ReactNode;
  morphType: 'scale-fade' | 'slide-fade' | 'rotate-fade';
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

interface StaggeredTransitionProps {
  visible: boolean;
  children: ReactNode[];
  staggerDelay?: number;
  animationType: 'fade' | 'slide' | 'scale';
  direction?: 'left' | 'right' | 'up' | 'down';
  style?: ViewStyle;
}

interface PageTransitionProps {
  visible: boolean;
  children: ReactNode;
  transitionType: 'slide' | 'fade' | 'scale' | 'flip' | 'cube';
  direction?: 'left' | 'right' | 'up' | 'down';
  duration?: number;
  style?: ViewStyle;
}

// Fade Transition Component
export const FadeTransition: React.FC<FadeTransitionProps> = ({
  visible,
  children,
  duration = 300,
  delay = 0,
  style,
}) => {
  const opacity = useSharedValue(visible ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  useEffect(() => {
    opacity.value = withDelay(
      delay,
      withTiming(visible ? 1 : 0, { duration, easing: Easing.out(Easing.cubic) })
    );
  }, [visible, duration, delay, opacity]);

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

// Slide Transition Component
export const SlideTransition: React.FC<SlideTransitionProps> = ({
  visible,
  children,
  direction,
  distance,
  duration = 400,
  delay = 0,
  style,
}) => {
  const defaultDistance = distance || (direction === 'left' || direction === 'right' ? screenWidth : screenHeight);
  
  const translateX = useSharedValue(
    direction === 'left' ? -defaultDistance : direction === 'right' ? defaultDistance : 0
  );
  const translateY = useSharedValue(
    direction === 'up' ? -defaultDistance : direction === 'down' ? defaultDistance : 0
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  useEffect(() => {
    if (visible) {
      translateX.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
      translateY.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
    } else {
      const targetX = direction === 'left' ? -defaultDistance : direction === 'right' ? defaultDistance : 0;
      const targetY = direction === 'up' ? -defaultDistance : direction === 'down' ? defaultDistance : 0;
      
      translateX.value = withTiming(targetX, { duration, easing: Easing.in(Easing.cubic) });
      translateY.value = withTiming(targetY, { duration, easing: Easing.in(Easing.cubic) });
    }
  }, [visible, direction, defaultDistance, duration, delay, translateX, translateY]);

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

// Scale Transition Component
export const ScaleTransition: React.FC<ScaleTransitionProps> = ({
  visible,
  children,
  fromScale = 0,
  toScale = 1,
  duration = 400,
  delay = 0,
  style,
}) => {
  const scale = useSharedValue(visible ? toScale : fromScale);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    scale.value = withDelay(
      delay,
      withSpring(visible ? toScale : fromScale, AnimationConfig.spring.bouncy)
    );
  }, [visible, fromScale, toScale, delay, scale]);

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

// Flip Transition Component
export const FlipTransition: React.FC<FlipTransitionProps> = ({
  visible,
  children,
  axis,
  duration = 600,
  delay = 0,
  style,
}) => {
  const rotation = useSharedValue(visible ? 0 : 180);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      axis === 'x' ? { rotateX: `${rotation.value}deg` } : { rotateY: `${rotation.value}deg` },
    ],
  }));

  useEffect(() => {
    rotation.value = withDelay(
      delay,
      withTiming(visible ? 0 : 180, { duration, easing: Easing.out(Easing.cubic) })
    );
  }, [visible, duration, delay, rotation]);

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

// Morph Transition Component
export const MorphTransition: React.FC<MorphTransitionProps> = ({
  visible,
  children,
  morphType,
  duration = 500,
  delay = 0,
  style,
}) => {
  const scale = useSharedValue(visible ? 1 : 0.8);
  const opacity = useSharedValue(visible ? 1 : 0);
  const translateY = useSharedValue(visible ? 0 : 20);
  const rotation = useSharedValue(visible ? 0 : 180);

  const animatedStyle = useAnimatedStyle(() => {
    switch (morphType) {
      case 'scale-fade':
        return {
          transform: [{ scale: scale.value }],
          opacity: opacity.value,
        };
      case 'slide-fade':
        return {
          transform: [{ translateY: translateY.value }],
          opacity: opacity.value,
        };
      case 'rotate-fade':
        return {
          transform: [{ rotate: `${rotation.value}deg` }],
          opacity: opacity.value,
        };
      default:
        return {
          transform: [{ scale: scale.value }],
          opacity: opacity.value,
        };
    }
  });

  useEffect(() => {
    const springConfig = AnimationConfig.spring.bouncy;
    const timingConfig = { duration, easing: Easing.out(Easing.cubic) };

    if (visible) {
      scale.value = withDelay(delay, withSpring(1, springConfig));
      opacity.value = withDelay(delay, withTiming(1, timingConfig));
      translateY.value = withDelay(delay, withSpring(0, springConfig));
      rotation.value = withDelay(delay, withTiming(0, timingConfig));
    } else {
      scale.value = withTiming(0.8, timingConfig);
      opacity.value = withTiming(0, timingConfig);
      translateY.value = withTiming(20, timingConfig);
      rotation.value = withTiming(180, timingConfig);
    }
  }, [visible, morphType, duration, delay, scale, opacity, translateY, rotation]);

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

// Staggered Transition Component
export const StaggeredTransition: React.FC<StaggeredTransitionProps> = ({
  visible,
  children,
  staggerDelay = 100,
  animationType,
  direction = 'up',
  style,
}) => {
  const animations = children.map(() => ({
    opacity: useSharedValue(visible ? 1 : 0),
    scale: useSharedValue(visible ? 1 : 0),
    translateX: useSharedValue(0),
    translateY: useSharedValue(0),
  }));

  useEffect(() => {
    children.forEach((_, index) => {
      const delay = index * staggerDelay;
      const animation = animations[index];

      if (visible) {
        switch (animationType) {
          case 'fade':
            animation.opacity.value = withDelay(delay, withTiming(1, AnimationConfig.timing.medium));
            break;
          case 'scale':
            animation.scale.value = withDelay(delay, withSpring(1, AnimationConfig.spring.bouncy));
            break;
          case 'slide':
            const distance = 50;
            const targetX = direction === 'left' ? -distance : direction === 'right' ? distance : 0;
            const targetY = direction === 'up' ? -distance : direction === 'down' ? distance : 0;
            
            animation.translateX.value = targetX;
            animation.translateY.value = targetY;
            animation.opacity.value = 0;
            
            animation.translateX.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
            animation.translateY.value = withDelay(delay, withSpring(0, AnimationConfig.spring.bouncy));
            animation.opacity.value = withDelay(delay, withTiming(1, AnimationConfig.timing.medium));
            break;
        }
      } else {
        animation.opacity.value = withTiming(0, AnimationConfig.timing.fast);
        animation.scale.value = withTiming(0, AnimationConfig.timing.fast);
      }
    });
  }, [visible, children, staggerDelay, animationType, direction, animations]);

  return (
    <View style={style}>
      {children.map((child, index) => {
        const animation = animations[index];
        
        const animatedStyle = useAnimatedStyle(() => ({
          opacity: animation.opacity.value,
          transform: [
            { scale: animation.scale.value },
            { translateX: animation.translateX.value },
            { translateY: animation.translateY.value },
          ],
        }));

        return (
          <Animated.View key={index} style={animatedStyle}>
            {child}
          </Animated.View>
        );
      })}
    </View>
  );
};

// Page Transition Component
export const PageTransition: React.FC<PageTransitionProps> = ({
  visible,
  children,
  transitionType,
  direction = 'right',
  duration = 500,
  style,
}) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const rotateY = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    switch (transitionType) {
      case 'slide':
        return {
          transform: [
            { translateX: translateX.value },
            { translateY: translateY.value },
          ],
        };
      case 'fade':
        return {
          opacity: opacity.value,
        };
      case 'scale':
        return {
          transform: [{ scale: scale.value }],
          opacity: opacity.value,
        };
      case 'flip':
        return {
          transform: [{ rotateY: `${rotateY.value}deg` }],
        };
      case 'cube':
        return {
          transform: [
            { translateX: translateX.value },
            { rotateY: `${rotateY.value}deg` },
          ],
        };
      default:
        return {};
    }
  });

  useEffect(() => {
    const timingConfig = { duration, easing: Easing.out(Easing.cubic) };
    const springConfig = AnimationConfig.spring.bouncy;

    if (visible) {
      // Enter animations
      switch (transitionType) {
        case 'slide':
          translateX.value = withSpring(0, springConfig);
          translateY.value = withSpring(0, springConfig);
          break;
        case 'fade':
          opacity.value = withTiming(1, timingConfig);
          break;
        case 'scale':
          scale.value = withSpring(1, springConfig);
          opacity.value = withTiming(1, timingConfig);
          break;
        case 'flip':
          rotateY.value = withTiming(0, timingConfig);
          break;
        case 'cube':
          translateX.value = withSpring(0, springConfig);
          rotateY.value = withTiming(0, timingConfig);
          break;
      }
    } else {
      // Exit animations
      switch (transitionType) {
        case 'slide':
          const slideDistance = direction === 'left' || direction === 'right' ? screenWidth : screenHeight;
          const targetX = direction === 'left' ? -slideDistance : direction === 'right' ? slideDistance : 0;
          const targetY = direction === 'up' ? -slideDistance : direction === 'down' ? slideDistance : 0;
          
          translateX.value = withTiming(targetX, timingConfig);
          translateY.value = withTiming(targetY, timingConfig);
          break;
        case 'fade':
          opacity.value = withTiming(0, timingConfig);
          break;
        case 'scale':
          scale.value = withTiming(0.8, timingConfig);
          opacity.value = withTiming(0, timingConfig);
          break;
        case 'flip':
          rotateY.value = withTiming(180, timingConfig);
          break;
        case 'cube':
          translateX.value = withTiming(direction === 'left' ? screenWidth : -screenWidth, timingConfig);
          rotateY.value = withTiming(direction === 'left' ? -90 : 90, timingConfig);
          break;
      }
    }
  }, [visible, transitionType, direction, duration, translateX, translateY, scale, opacity, rotateY]);

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

export default {
  FadeTransition,
  SlideTransition,
  ScaleTransition,
  FlipTransition,
  MorphTransition,
  StaggeredTransition,
  PageTransition,
};
