import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedProps, 
  withTiming, 
  Easing 
} from 'react-native-reanimated';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Colors, FontSizes, FontWeights } from '../constants/Colors';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface RadialGaugeProps {
  value: number; // 0-100
  size?: number;
  strokeWidth?: number;
}

const RadialGauge: React.FC<RadialGaugeProps> = ({ 
  value, 
  size = 64, 
  strokeWidth = 6 
}) => {
  const animatedValue = useSharedValue(0);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    animatedValue.value = withTiming(value, {
      duration: 1500,
      easing: Easing.out(Easing.cubic),
    });
  }, [value, animatedValue]);

  const animatedProps = useAnimatedProps(() => {
    const strokeDashoffset = circumference - (animatedValue.value / 100) * circumference;
    return {
      strokeDashoffset,
    };
  });

  const displayValue = Math.round(value / 10);

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <Defs>
          <LinearGradient id="gauge-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <Stop offset="0%" stopColor={Colors.secondary} />
            <Stop offset="100%" stopColor={Colors.primary} />
          </LinearGradient>
        </Defs>
        
        {/* Background circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="transparent"
          stroke={Colors.gray200}
          strokeWidth={strokeWidth}
        />
        
        {/* Animated progress circle */}
        <AnimatedCircle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="transparent"
          stroke="url(#gauge-gradient)"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={circumference}
          animatedProps={animatedProps}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </Svg>
      
      <View style={styles.valueContainer}>
        <Text style={styles.valueText}>{displayValue}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  valueContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  valueText: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.text,
  },
});

export default RadialGauge;
