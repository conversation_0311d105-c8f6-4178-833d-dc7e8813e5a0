import { Pedometer } from 'expo-sensors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';

export interface HealthData {
  heartRate: number;
  steps: number;
  timestamp: number;
  date: string;
}

export interface HeartRateReading {
  bpm: number;
  timestamp: number;
  confidence: number;
}

export interface StepsData {
  steps: number;
  distance: number;
  calories: number;
  timestamp: number;
}

class HealthService {
  private static instance: HealthService;
  private heartRateSubscription: any = null;
  private pedometerSubscription: any = null;
  private currentHeartRate: number = 0;
  private currentSteps: number = 0;
  private isMonitoring: boolean = false;
  private useSimulation: boolean = false;
  private baseSteps: number = 0;
  private startTime: number = 0;

  private constructor() {}

  static getInstance(): HealthService {
    if (!HealthService.instance) {
      HealthService.instance = new HealthService();
    }
    return HealthService.instance;
  }

  // Initialize health monitoring with real sensors
  async initializeHealthMonitoring(): Promise<boolean> {
    try {
      // Check if pedometer is available
      const isAvailable = await Pedometer.isAvailableAsync();
      if (!isAvailable) {
        console.warn('Pedometer not available, falling back to simulation');
        this.useSimulation = true;
        return true;
      }

      // Request permissions
      const { status } = await Pedometer.requestPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Pedometer permission not granted, falling back to simulation');
        this.useSimulation = true;
        return true;
      }

      // Test if we can use real step counting (some Android devices have limitations)
      try {
        // Try to start watching steps to see if it works
        const testSubscription = Pedometer.watchStepCount(() => {});
        testSubscription.remove(); // Immediately remove test subscription

        console.log('Real health monitoring initialized successfully');
        this.useSimulation = false;
        return true;
      } catch (testError) {
        console.warn('Step counting not fully supported on this device, falling back to simulation');
        this.useSimulation = true;
        return true;
      }

    } catch (error) {
      console.error('Error initializing health monitoring, falling back to simulation:', error);
      this.useSimulation = true;
      return true;
    }
  }

  // Start monitoring heart rate and steps
  async startMonitoring(): Promise<void> {
    try {
      if (this.isMonitoring) {
        return;
      }

      this.isMonitoring = true;
      this.startTime = Date.now();

      if (this.useSimulation) {
        // Use simulation for development/testing
        this.baseSteps = Math.floor(Math.random() * 5000) + 2000;
        await this.startStepSimulation();
        console.log('Health monitoring started (simulated)');
      } else {
        // Use real sensors
        await this.startRealStepCounting();
        console.log('Health monitoring started (real sensors)');
      }

      // Always simulate heart rate (requires special hardware)
      await this.startHeartRateSimulation();

      // Provide haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error starting health monitoring:', error);
      this.isMonitoring = false;
    }
  }

  // Stop monitoring
  async stopMonitoring(): Promise<void> {
    try {
      this.isMonitoring = false;

      if (this.pedometerSubscription) {
        this.pedometerSubscription.remove();
        this.pedometerSubscription = null;
      }

      if (this.heartRateSubscription) {
        clearInterval(this.heartRateSubscription);
        this.heartRateSubscription = null;
      }

      console.log('Health monitoring stopped');
    } catch (error) {
      console.error('Error stopping health monitoring:', error);
    }
  }

  // Real step counting using Expo Pedometer
  private async startRealStepCounting(): Promise<void> {
    try {
      // Initialize step count from stored data or start from 0
      const today = new Date().toISOString().split('T')[0];
      const storedSteps = await this.getTodayStoredSteps(today);
      this.currentSteps = storedSteps;

      // Subscribe to real-time step updates (this works on both iOS and Android)
      this.pedometerSubscription = Pedometer.watchStepCount((result) => {
        // For Android, we get incremental steps, so we add to our base
        this.currentSteps = storedSteps + result.steps;
        this.saveHealthData();
      });

      console.log('Real step counting started successfully');

    } catch (error) {
      console.error('Error starting real step counting, falling back to simulation:', error);
      this.useSimulation = true;
      await this.startStepSimulation();
    }
  }

  // Get today's stored steps from AsyncStorage
  private async getTodayStoredSteps(date: string): Promise<number> {
    try {
      const key = `health_data_${date}`;
      const data = await AsyncStorage.getItem(key);

      if (!data) {
        return 0;
      }

      const parsedData: HealthData[] = JSON.parse(data);

      if (parsedData.length === 0) {
        return 0;
      }

      // Return the latest step count for today
      return Math.max(...parsedData.map(reading => reading.steps));
    } catch (error) {
      console.error('Error getting stored steps:', error);
      return 0;
    }
  }

  // Simulate step counting for demo purposes
  private async startStepSimulation(): Promise<void> {
    try {
      // Simulate realistic step counting
      const interval = setInterval(() => {
        if (this.isMonitoring) {
          // Add 1-3 steps every 10 seconds (realistic walking pace)
          const additionalSteps = Math.floor(Math.random() * 3) + 1;
          this.currentSteps = this.baseSteps + Math.floor((Date.now() - this.startTime) / 10000) * additionalSteps;
          this.saveHealthData();
        } else {
          clearInterval(interval);
        }
      }, 10000); // Update every 10 seconds

    } catch (error) {
      console.error('Error starting step simulation:', error);
    }
  }

  // Simulate heart rate monitoring (for demo purposes)
  private async startHeartRateSimulation(): Promise<void> {
    // Generate realistic heart rate data
    const generateHeartRate = () => {
      const baseRate = 72; // Average resting heart rate
      const variation = Math.random() * 20 - 10; // ±10 bpm variation
      const timeOfDay = new Date().getHours();
      
      // Adjust for time of day
      let adjustment = 0;
      if (timeOfDay >= 6 && timeOfDay <= 10) {
        adjustment = 5; // Morning activity
      } else if (timeOfDay >= 18 && timeOfDay <= 22) {
        adjustment = 8; // Evening activity
      }

      return Math.round(baseRate + variation + adjustment);
    };

    this.currentHeartRate = generateHeartRate();

    // Update heart rate every 5 seconds
    this.heartRateSubscription = setInterval(() => {
      this.currentHeartRate = generateHeartRate();
      this.saveHealthData();
    }, 5000);
  }

  // Get current health data
  getCurrentHealthData(): HealthData {
    return {
      heartRate: this.currentHeartRate,
      steps: this.currentSteps,
      timestamp: Date.now(),
      date: new Date().toISOString().split('T')[0],
    };
  }

  // Get heart rate reading
  getCurrentHeartRate(): HeartRateReading {
    return {
      bpm: this.currentHeartRate,
      timestamp: Date.now(),
      confidence: 0.85 + Math.random() * 0.15, // 85-100% confidence
    };
  }

  // Get steps data
  getCurrentSteps(): StepsData {
    const steps = this.currentSteps;
    const avgStepLength = 0.762; // meters
    const distance = steps * avgStepLength; // in meters
    const calories = steps * 0.04; // rough estimate: 0.04 calories per step

    return {
      steps,
      distance: Math.round(distance),
      calories: Math.round(calories),
      timestamp: Date.now(),
    };
  }

  // Save health data to local storage
  private async saveHealthData(): Promise<void> {
    try {
      const healthData = this.getCurrentHealthData();
      const key = `health_data_${healthData.date}`;
      
      // Get existing data for today
      const existingData = await AsyncStorage.getItem(key);
      let dailyData = existingData ? JSON.parse(existingData) : [];
      
      // Add new reading
      dailyData.push(healthData);
      
      // Keep only last 100 readings per day
      if (dailyData.length > 100) {
        dailyData = dailyData.slice(-100);
      }
      
      await AsyncStorage.setItem(key, JSON.stringify(dailyData));
    } catch (error) {
      console.error('Error saving health data:', error);
    }
  }

  // Get historical health data
  async getHistoricalData(days: number = 7): Promise<HealthData[]> {
    try {
      const data: HealthData[] = [];
      const today = new Date();
      
      for (let i = 0; i < days; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateString = date.toISOString().split('T')[0];
        const key = `health_data_${dateString}`;
        
        const dayData = await AsyncStorage.getItem(key);
        if (dayData) {
          const parsedData = JSON.parse(dayData);
          data.push(...parsedData);
        }
      }
      
      return data.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Error getting historical data:', error);
      return [];
    }
  }

  // Get daily averages
  async getDailyAverages(date: string): Promise<{ avgHeartRate: number; totalSteps: number }> {
    try {
      const key = `health_data_${date}`;
      const data = await AsyncStorage.getItem(key);
      
      if (!data) {
        return { avgHeartRate: 0, totalSteps: 0 };
      }
      
      const parsedData: HealthData[] = JSON.parse(data);
      
      if (parsedData.length === 0) {
        return { avgHeartRate: 0, totalSteps: 0 };
      }
      
      const avgHeartRate = Math.round(
        parsedData.reduce((sum, reading) => sum + reading.heartRate, 0) / parsedData.length
      );
      
      const totalSteps = Math.max(...parsedData.map(reading => reading.steps));
      
      return { avgHeartRate, totalSteps };
    } catch (error) {
      console.error('Error getting daily averages:', error);
      return { avgHeartRate: 0, totalSteps: 0 };
    }
  }

  // Check if monitoring is active
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  // Get health status based on current readings
  getHealthStatus(): {
    heartRateStatus: 'low' | 'normal' | 'high';
    stepsStatus: 'low' | 'normal' | 'high';
    overallStatus: 'excellent' | 'good' | 'fair' | 'poor';
  } {
    const heartRate = this.currentHeartRate;
    const steps = this.currentSteps;

    // Heart rate status
    let heartRateStatus: 'low' | 'normal' | 'high' = 'normal';
    if (heartRate < 60) heartRateStatus = 'low';
    else if (heartRate > 100) heartRateStatus = 'high';

    // Steps status (based on daily goal of 10,000 steps)
    let stepsStatus: 'low' | 'normal' | 'high' = 'normal';
    if (steps < 5000) stepsStatus = 'low';
    else if (steps > 12000) stepsStatus = 'high';

    // Overall status
    let overallStatus: 'excellent' | 'good' | 'fair' | 'poor' = 'good';
    if (heartRateStatus === 'normal' && stepsStatus === 'high') {
      overallStatus = 'excellent';
    } else if (heartRateStatus === 'normal' && stepsStatus === 'normal') {
      overallStatus = 'good';
    } else if (heartRateStatus !== 'normal' || stepsStatus === 'low') {
      overallStatus = 'fair';
    } else {
      overallStatus = 'poor';
    }

    return { heartRateStatus, stepsStatus, overallStatus };
  }
}

export default HealthService.getInstance();
