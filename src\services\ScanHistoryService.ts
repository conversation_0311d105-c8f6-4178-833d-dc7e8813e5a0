// Using in-memory storage for Expo Go compatibility
// In production, replace with AsyncStorage or SecureStore

export interface DetectedFood {
  name: string;
  portion: string;
  confidence: number;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sugar?: number;
  sodium?: number;
  ingredients?: string[];
  cookingMethod?: string;
  freshness?: string;
}

export interface ScanResult {
  id: string;
  timestamp: Date;
  imageUri: string;
  detectedFoods: DetectedFood[];
  totalNutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  notes?: string;
  location?: string;
}

export interface DailyIntake {
  date: string;
  scans: ScanResult[];
  totalNutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sugar: number;
    sodium: number;
  };
  goals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  progress: {
    caloriesPercent: number;
    proteinPercent: number;
    carbsPercent: number;
    fatPercent: number;
  };
}

// In-memory storage for Expo Go compatibility
class InMemoryStorage {
  private static data: Record<string, string> = {};

  static async setItem(key: string, value: string): Promise<void> {
    this.data[key] = value;
  }

  static async getItem(key: string): Promise<string | null> {
    return this.data[key] || null;
  }

  static async removeItem(key: string): Promise<void> {
    delete this.data[key];
  }
}

class ScanHistoryService {
  private static readonly STORAGE_KEY = 'scan_history';
  private static readonly DAILY_INTAKE_KEY = 'daily_intake';

  // Save scan result
  static async saveScan(scanResult: ScanResult): Promise<void> {
    try {
      const existingScans = await this.getAllScans();
      const updatedScans = [scanResult, ...existingScans];
      
      // Keep only last 100 scans to manage storage
      const limitedScans = updatedScans.slice(0, 100);
      
      await InMemoryStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedScans));
      
      // Update daily intake
      await this.updateDailyIntake(scanResult);
    } catch (error) {
      console.error('Error saving scan:', error);
      throw error;
    }
  }

  // Get all scans
  static async getAllScans(): Promise<ScanResult[]> {
    try {
      const scansJson = await InMemoryStorage.getItem(this.STORAGE_KEY);
      if (!scansJson) return [];
      
      const scans = JSON.parse(scansJson);
      // Convert timestamp strings back to Date objects
      return scans.map((scan: any) => ({
        ...scan,
        timestamp: new Date(scan.timestamp)
      }));
    } catch (error) {
      console.error('Error getting scans:', error);
      return [];
    }
  }

  // Get scans for specific date
  static async getScansForDate(date: string): Promise<ScanResult[]> {
    try {
      const allScans = await this.getAllScans();
      return allScans.filter(scan => 
        scan.timestamp.toDateString() === new Date(date).toDateString()
      );
    } catch (error) {
      console.error('Error getting scans for date:', error);
      return [];
    }
  }

  // Get today's scans
  static async getTodaysScans(): Promise<ScanResult[]> {
    const today = new Date().toDateString();
    return this.getScansForDate(today);
  }

  // Update daily intake
  private static async updateDailyIntake(newScan: ScanResult): Promise<void> {
    try {
      const today = new Date().toDateString();
      const todaysScans = await this.getScansForDate(today);
      
      // Calculate total nutrition for the day
      const totalNutrition = todaysScans.reduce((total, scan) => ({
        calories: total.calories + scan.totalNutrition.calories,
        protein: total.protein + scan.totalNutrition.protein,
        carbs: total.carbs + scan.totalNutrition.carbs,
        fat: total.fat + scan.totalNutrition.fat,
        fiber: total.fiber + scan.totalNutrition.fiber,
        sugar: total.sugar + scan.totalNutrition.sugar,
        sodium: total.sodium + scan.totalNutrition.sodium,
      }), {
        calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sugar: 0, sodium: 0
      });

      // Default daily goals (should come from user profile)
      const goals = {
        calories: 2000,
        protein: 150,
        carbs: 250,
        fat: 65,
      };

      // Calculate progress percentages
      const progress = {
        caloriesPercent: Math.round((totalNutrition.calories / goals.calories) * 100),
        proteinPercent: Math.round((totalNutrition.protein / goals.protein) * 100),
        carbsPercent: Math.round((totalNutrition.carbs / goals.carbs) * 100),
        fatPercent: Math.round((totalNutrition.fat / goals.fat) * 100),
      };

      const dailyIntake: DailyIntake = {
        date: today,
        scans: todaysScans,
        totalNutrition,
        goals,
        progress,
      };

      await InMemoryStorage.setItem(this.DAILY_INTAKE_KEY, JSON.stringify(dailyIntake));
    } catch (error) {
      console.error('Error updating daily intake:', error);
    }
  }

  // Get daily intake
  static async getDailyIntake(date?: string): Promise<DailyIntake | null> {
    try {
      const targetDate = date || new Date().toDateString();
      const dailyIntakeJson = await InMemoryStorage.getItem(this.DAILY_INTAKE_KEY);
      
      if (!dailyIntakeJson) return null;
      
      const dailyIntake = JSON.parse(dailyIntakeJson);
      
      // If requesting different date, recalculate
      if (dailyIntake.date !== targetDate) {
        const scansForDate = await this.getScansForDate(targetDate);
        if (scansForDate.length === 0) return null;
        
        // Recalculate for requested date
        // This is a simplified version - in production, you'd store daily intake for each date
        return null;
      }
      
      return dailyIntake;
    } catch (error) {
      console.error('Error getting daily intake:', error);
      return null;
    }
  }

  // Delete scan
  static async deleteScan(scanId: string): Promise<void> {
    try {
      const allScans = await this.getAllScans();
      const filteredScans = allScans.filter(scan => scan.id !== scanId);
      await InMemoryStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredScans));
    } catch (error) {
      console.error('Error deleting scan:', error);
      throw error;
    }
  }

  // Get nutrition trends (last 7 days)
  static async getNutritionTrends(): Promise<any[]> {
    try {
      const allScans = await this.getAllScans();
      const last7Days = [];
      
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateString = date.toDateString();
        
        const dayScans = allScans.filter(scan => 
          scan.timestamp.toDateString() === dateString
        );
        
        const dayTotal = dayScans.reduce((total, scan) => ({
          calories: total.calories + scan.totalNutrition.calories,
          protein: total.protein + scan.totalNutrition.protein,
          carbs: total.carbs + scan.totalNutrition.carbs,
          fat: total.fat + scan.totalNutrition.fat,
        }), { calories: 0, protein: 0, carbs: 0, fat: 0 });
        
        last7Days.push({
          date: dateString,
          ...dayTotal,
          scansCount: dayScans.length,
        });
      }
      
      return last7Days;
    } catch (error) {
      console.error('Error getting nutrition trends:', error);
      return [];
    }
  }

  // Clear all data
  static async clearAllData(): Promise<void> {
    try {
      await InMemoryStorage.removeItem(this.STORAGE_KEY);
      await InMemoryStorage.removeItem(this.DAILY_INTAKE_KEY);
    } catch (error) {
      console.error('Error clearing data:', error);
      throw error;
    }
  }
}

export default ScanHistoryService;
