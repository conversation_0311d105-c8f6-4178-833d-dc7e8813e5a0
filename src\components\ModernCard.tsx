import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  ImageBackground,
  ImageSourcePropType,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  FadeInUp,
  FadeInDown,
  SlideInRight,
  SlideInLeft,
} from 'react-native-reanimated';
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, AnimationConfig, BlurIntensity } from '../constants/Colors';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';

interface ModernCardProps {
  title: string;
  description?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  onPress?: () => void;
  variant?: 'default' | 'glass' | 'glassMorphism' | 'gradient' | 'elevated' | 'minimal' | 'hero' | 'premium' | 'floating';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  backgroundImage?: ImageSourcePropType;
  children?: React.ReactNode;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
  disabled?: boolean;
  loading?: boolean;
  animationDelay?: number;
  animationType?: 'fadeUp' | 'fadeDown' | 'slideLeft' | 'slideRight' | 'zoomIn' | 'bounceIn' | 'none';
  shadowIntensity?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'brand' | 'floating';
  borderRadius?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'xxxl' | 'pill' | 'full';
  glowEffect?: boolean;
  hapticFeedback?: boolean;
  blurIntensity?: 'light' | 'medium' | 'strong' | 'ultra';
  pressAnimation?: 'scale' | 'depth' | 'glow' | 'none';
  microInteractions?: boolean;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export const ModernCard: React.FC<ModernCardProps> = ({
  title,
  description,
  icon,
  onPress,
  variant = 'default',
  size = 'md',
  backgroundImage,
  children,
  style,
  titleStyle,
  descriptionStyle,
  disabled = false,
  loading = false,
  animationDelay = 0,
  animationType = 'fadeUp',
  shadowIntensity = 'medium',
  borderRadius = 'lg',
  glowEffect = false,
  hapticFeedback = true,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const glowOpacity = useSharedValue(0);
  const shadowScale = useSharedValue(1);
  const rotateY = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotateY: `${rotateY.value}deg` },
    ],
    opacity: opacity.value,
  }));

  const glowAnimatedStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
    transform: [{ scale: interpolate(glowOpacity.value, [0, 1], [0.95, 1.05]) }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: shadowScale.value }],
  }));

  const triggerHaptic = (intensity: 'light' | 'medium' | 'heavy' = 'light') => {
    if (hapticFeedback) {
      switch (intensity) {
        case 'light':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
      }
    }
  };

  const handlePressIn = () => {
    'worklet';
    scale.value = withSpring(0.98, { damping: 20, stiffness: 400 });
    opacity.value = withTiming(0.9, { duration: 150 });
    
    if (glowEffect) {
      glowOpacity.value = withTiming(0.4, { duration: 150 });
    }
    
    shadowScale.value = withSpring(1.02, { damping: 15, stiffness: 300 });
    runOnJS(triggerHaptic)();
  };

  const handlePressOut = () => {
    'worklet';
    scale.value = withSpring(1, { damping: 20, stiffness: 400 });
    opacity.value = withTiming(1, { duration: 150 });
    
    if (glowEffect) {
      glowOpacity.value = withTiming(0, { duration: 300 });
    }
    
    shadowScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  const handlePress = () => {
    'worklet';
    rotateY.value = withSequence(
      withTiming(1, { duration: 100 }),
      withTiming(-1, { duration: 100 }),
      withTiming(0, { duration: 100 })
    );
    
    if (onPress) {
      runOnJS(onPress)();
    }
  };

  const getAnimationProps = () => {
    const baseDelay = animationDelay;
    switch (animationType) {
      case 'fadeUp':
        return { entering: FadeInUp.delay(baseDelay).duration(600) };
      case 'fadeDown':
        return { entering: FadeInDown.delay(baseDelay).duration(600) };
      case 'slideLeft':
        return { entering: SlideInLeft.delay(baseDelay).duration(600) };
      case 'slideRight':
        return { entering: SlideInRight.delay(baseDelay).duration(600) };
      default:
        return {};
    }
  };

  const getCardStyle = () => {
    return [
      styles.card,
      styles[`${size}Card`],
      styles[`${variant}Card`],
      styles[`${borderRadius}BorderRadius`],
      styles[`${shadowIntensity}Shadow`],
      disabled && styles.disabled,
      style,
    ];
  };

  const renderContent = () => (
    <View style={styles.content}>
      {icon && (
        <View style={[styles.iconContainer, styles[`${variant}IconContainer`]]}>
          <Ionicons
            name={icon}
            size={getIconSize()}
            color={getIconColor()}
          />
        </View>
      )}
      
      <View style={styles.textContainer}>
        <Text style={[styles.title, styles[`${size}Title`], styles[`${variant}Title`], titleStyle]}>
          {title}
        </Text>
        {description && (
          <Text style={[styles.description, styles[`${size}Description`], styles[`${variant}Description`], descriptionStyle]}>
            {description}
          </Text>
        )}
      </View>
      
      {children}
    </View>
  );

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 16;
      case 'lg': return 28;
      case 'xl': return 32;
      default: return 24;
    }
  };

  const getIconColor = () => {
    if (disabled) return Colors.mutedForeground;
    
    switch (variant) {
      case 'glass': return Colors.brand;
      case 'gradient': return Colors.brandForeground;
      case 'hero': return Colors.brandForeground;
      default: return Colors.brand;
    }
  };

  if (!onPress) {
    return (
      <Animated.View style={[getCardStyle(), animatedStyle]} {...getAnimationProps()}>
        {backgroundImage && (
          <ImageBackground
            source={backgroundImage}
            style={styles.backgroundImage}
            imageStyle={styles.backgroundImageStyle}
          />
        )}
        {variant === 'gradient' && (
          <LinearGradient
            colors={[Colors.brand, Colors.brandSecondary]}
            style={styles.gradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        )}
        {renderContent()}
      </Animated.View>
    );
  }

  return (
    <Animated.View {...getAnimationProps()}>
      {glowEffect && (
        <Animated.View style={[getCardStyle(), styles.glowContainer, glowAnimatedStyle]}>
          <LinearGradient
            colors={[Colors.brandGlow, 'transparent']}
            style={styles.glow}
          />
        </Animated.View>
      )}
      
      <Animated.View style={[shadowAnimatedStyle]}>
        <AnimatedTouchableOpacity
          style={[getCardStyle(), animatedStyle]}
          onPress={handlePress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || loading}
          activeOpacity={1}
        >
          {backgroundImage && (
            <ImageBackground
              source={backgroundImage}
              style={styles.backgroundImage}
              imageStyle={styles.backgroundImageStyle}
            />
          )}
          {variant === 'gradient' && (
            <LinearGradient
              colors={[Colors.brand, Colors.brandSecondary]}
              style={styles.gradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          )}
          {renderContent()}
        </AnimatedTouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Base Card Styles
  card: {
    overflow: 'hidden',
    position: 'relative',
  },
  
  // Size Variants
  smCard: {
    padding: Spacing.lg,
    minHeight: 80,
  },
  mdCard: {
    padding: Spacing.xl,
    minHeight: 120,
  },
  lgCard: {
    padding: Spacing.xxl,
    minHeight: 160,
  },
  xlCard: {
    padding: Spacing.xxxl,
    minHeight: 200,
  },
  
  // Variant Styles
  defaultCard: {
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  glassCard: {
    backgroundColor: Colors.glass,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
    backdropFilter: 'blur(20px)',
  },
  gradientCard: {
    backgroundColor: 'transparent',
  },
  elevatedCard: {
    backgroundColor: Colors.card,
    elevation: 8,
  },
  minimalCard: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  heroCard: {
    backgroundColor: Colors.brand,
  },
  
  // Border Radius Variants
  smBorderRadius: { borderRadius: BorderRadius.sm },
  mdBorderRadius: { borderRadius: BorderRadius.md },
  lgBorderRadius: { borderRadius: BorderRadius.lg },
  xlBorderRadius: { borderRadius: BorderRadius.xl },
  xxlBorderRadius: { borderRadius: BorderRadius.xxl },
  
  // Shadow Variants
  noneShadow: {},
  subtleShadow: Shadows.xs,
  mediumShadow: Shadows.md,
  strongShadow: Shadows.lg,
  
  // Content Layout
  content: {
    flex: 1,
    zIndex: 2,
  },
  iconContainer: {
    marginBottom: Spacing.md,
  },
  textContainer: {
    flex: 1,
  },
  
  // Background Elements
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  backgroundImageStyle: {
    borderRadius: BorderRadius.lg,
    opacity: 0.1,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  
  // Glow Effect
  glowContainer: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    zIndex: -1,
  },
  glow: {
    flex: 1,
    borderRadius: BorderRadius.xl,
  },
  
  // Text Styles
  title: {
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  description: {
    fontWeight: FontWeights.normal,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  
  // Size-specific text styles
  smTitle: { fontSize: FontSizes.sm },
  mdTitle: { fontSize: FontSizes.base },
  lgTitle: { fontSize: FontSizes.lg },
  xlTitle: { fontSize: FontSizes.xl },
  
  smDescription: { fontSize: FontSizes.xs },
  mdDescription: { fontSize: FontSizes.sm },
  lgDescription: { fontSize: FontSizes.base },
  xlDescription: { fontSize: FontSizes.lg },
  
  // Variant-specific text styles
  glassTitle: { color: Colors.foreground },
  glassDescription: { color: Colors.mutedForeground },
  gradientTitle: { color: Colors.brandForeground },
  gradientDescription: { color: Colors.brandForeground, opacity: 0.8 },
  heroTitle: { color: Colors.brandForeground },
  heroDescription: { color: Colors.brandForeground, opacity: 0.8 },
  
  // Icon Containers
  defaultIconContainer: {},
  glassIconContainer: {},
  gradientIconContainer: {},
  elevatedIconContainer: {},
  minimalIconContainer: {},
  heroIconContainer: {},
  
  // States
  disabled: {
    opacity: 0.5,
  },
});
