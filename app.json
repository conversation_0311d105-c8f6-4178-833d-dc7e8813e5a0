{"expo": {"name": "nutri-ai-mobile", "slug": "nutri-ai-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "platforms": ["ios", "android"], "sdkVersion": "49.0.0", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSMotionUsageDescription": "This app uses motion sensors to track your steps and activity for health monitoring.", "NSHealthShareUsageDescription": "This app needs access to health data to provide personalized nutrition recommendations.", "NSHealthUpdateUsageDescription": "This app needs to update health data to track your nutrition progress."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.ACTIVITY_RECOGNITION", "android.permission.BODY_SENSORS", "com.google.android.gms.permission.ACTIVITY_RECOGNITION", "android.permission.ACTIVITY_RECOGNITION", "android.permission.BODY_SENSORS", "com.google.android.gms.permission.ACTIVITY_RECOGNITION"], "package": "com.saim1111.nutriaimobile", "versionCode": 1}, "plugins": ["expo-dev-client"], "extra": {"eas": {"projectId": "a0522ece-4ac5-44c8-b9b6-9cfb3eb83ac2"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/a0522ece-4ac5-44c8-b9b6-9cfb3eb83ac2"}}}