import React, { ReactNode } from 'react';
import { View, ViewStyle } from 'react-native';
import {
  PanG<PERSON>ure<PERSON><PERSON><PERSON>,
  Tap<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Long<PERSON>ressGesture<PERSON><PERSON><PERSON>,
  Pinch<PERSON>esture<PERSON><PERSON>ler,
  RotationGestureHandler,
  State,
} from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
} from 'react-native-reanimated';
import { Haptics } from 'expo-haptics';
import { AnimationConfig } from '../utils/AnimationUtils';

interface SwipeableCardProps {
  children: ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  swipeThreshold?: number;
  style?: ViewStyle;
}

interface PressableScaleProps {
  children: ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  scaleValue?: number;
  hapticFeedback?: boolean;
  style?: ViewStyle;
}

interface DraggableProps {
  children: ReactNode;
  onDragEnd?: (x: number, y: number) => void;
  boundaryPadding?: number;
  snapBack?: boolean;
  style?: ViewStyle;
}

interface PinchZoomProps {
  children: ReactNode;
  minScale?: number;
  maxScale?: number;
  style?: ViewStyle;
}

interface RotatableProps {
  children: ReactNode;
  onRotationEnd?: (rotation: number) => void;
  style?: ViewStyle;
}

// Swipeable Card Component
export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  swipeThreshold = 100,
  style,
}) => {
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: () => {
      runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
    },
    onActive: (event) => {
      translateX.value = event.translationX;
      opacity.value = interpolate(
        Math.abs(event.translationX),
        [0, swipeThreshold],
        [1, 0.7],
        'clamp'
      );
    },
    onEnd: (event) => {
      const shouldSwipeLeft = event.translationX < -swipeThreshold;
      const shouldSwipeRight = event.translationX > swipeThreshold;

      if (shouldSwipeLeft && onSwipeLeft) {
        translateX.value = withTiming(-300, AnimationConfig.timing.fast);
        opacity.value = withTiming(0, AnimationConfig.timing.fast);
        runOnJS(onSwipeLeft)();
      } else if (shouldSwipeRight && onSwipeRight) {
        translateX.value = withTiming(300, AnimationConfig.timing.fast);
        opacity.value = withTiming(0, AnimationConfig.timing.fast);
        runOnJS(onSwipeRight)();
      } else {
        translateX.value = withSpring(0, AnimationConfig.spring.bouncy);
        opacity.value = withTiming(1, AnimationConfig.timing.medium);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
    opacity: opacity.value,
  }));

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[style, animatedStyle]}>
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

// Pressable Scale Component
export const PressableScale: React.FC<PressableScaleProps> = ({
  children,
  onPress,
  onLongPress,
  scaleValue = 0.95,
  hapticFeedback = true,
  style,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const tapHandler = useAnimatedGestureHandler({
    onStart: () => {
      scale.value = withSpring(scaleValue, AnimationConfig.spring.snappy);
      opacity.value = withTiming(0.8, AnimationConfig.timing.fast);
      
      if (hapticFeedback) {
        runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
      }
    },
    onEnd: () => {
      scale.value = withSpring(1, AnimationConfig.spring.bouncy);
      opacity.value = withTiming(1, AnimationConfig.timing.fast);
      
      if (onPress) {
        runOnJS(onPress)();
      }
    },
    onFail: () => {
      scale.value = withSpring(1, AnimationConfig.spring.bouncy);
      opacity.value = withTiming(1, AnimationConfig.timing.fast);
    },
  });

  const longPressHandler = useAnimatedGestureHandler({
    onStart: () => {
      scale.value = withSpring(1.05, AnimationConfig.spring.gentle);
      
      if (hapticFeedback) {
        runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
      }
    },
    onEnd: () => {
      scale.value = withSpring(1, AnimationConfig.spring.bouncy);
      
      if (onLongPress) {
        runOnJS(onLongPress)();
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <LongPressGestureHandler onGestureEvent={longPressHandler} minDurationMs={500}>
      <TapGestureHandler onGestureEvent={tapHandler}>
        <Animated.View style={[style, animatedStyle]}>
          {children}
        </Animated.View>
      </TapGestureHandler>
    </LongPressGestureHandler>
  );
};

// Draggable Component
export const Draggable: React.FC<DraggableProps> = ({
  children,
  onDragEnd,
  boundaryPadding = 20,
  snapBack = false,
  style,
}) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const startX = useSharedValue(0);
  const startY = useSharedValue(0);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: () => {
      startX.value = translateX.value;
      startY.value = translateY.value;
      runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
    },
    onActive: (event) => {
      translateX.value = startX.value + event.translationX;
      translateY.value = startY.value + event.translationY;
    },
    onEnd: () => {
      if (snapBack) {
        translateX.value = withSpring(0, AnimationConfig.spring.bouncy);
        translateY.value = withSpring(0, AnimationConfig.spring.bouncy);
      }
      
      if (onDragEnd) {
        runOnJS(onDragEnd)(translateX.value, translateY.value);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[style, animatedStyle]}>
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

// Pinch Zoom Component
export const PinchZoom: React.FC<PinchZoomProps> = ({
  children,
  minScale = 0.5,
  maxScale = 3,
  style,
}) => {
  const scale = useSharedValue(1);
  const baseScale = useSharedValue(1);

  const pinchHandler = useAnimatedGestureHandler({
    onStart: () => {
      baseScale.value = scale.value;
    },
    onActive: (event) => {
      const newScale = baseScale.value * event.scale;
      scale.value = interpolate(
        newScale,
        [minScale, maxScale],
        [minScale, maxScale],
        'clamp'
      );
    },
    onEnd: () => {
      if (scale.value < 1) {
        scale.value = withSpring(1, AnimationConfig.spring.bouncy);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <PinchGestureHandler onGestureEvent={pinchHandler}>
      <Animated.View style={[style, animatedStyle]}>
        {children}
      </Animated.View>
    </PinchGestureHandler>
  );
};

// Rotatable Component
export const Rotatable: React.FC<RotatableProps> = ({
  children,
  onRotationEnd,
  style,
}) => {
  const rotation = useSharedValue(0);
  const baseRotation = useSharedValue(0);

  const rotationHandler = useAnimatedGestureHandler({
    onStart: () => {
      baseRotation.value = rotation.value;
    },
    onActive: (event) => {
      rotation.value = baseRotation.value + event.rotation;
    },
    onEnd: () => {
      // Snap to nearest 90 degrees
      const snapAngle = Math.round(rotation.value / (Math.PI / 2)) * (Math.PI / 2);
      rotation.value = withSpring(snapAngle, AnimationConfig.spring.bouncy);
      
      if (onRotationEnd) {
        runOnJS(onRotationEnd)(snapAngle);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}rad` }],
  }));

  return (
    <RotationGestureHandler onGestureEvent={rotationHandler}>
      <Animated.View style={[style, animatedStyle]}>
        {children}
      </Animated.View>
    </RotationGestureHandler>
  );
};

// Pull to Refresh Component
interface PullToRefreshProps {
  children: ReactNode;
  onRefresh: () => void;
  refreshThreshold?: number;
  style?: ViewStyle;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  refreshThreshold = 100,
  style,
}) => {
  const translateY = useSharedValue(0);
  const refreshing = useSharedValue(false);

  const gestureHandler = useAnimatedGestureHandler({
    onActive: (event) => {
      if (event.translationY > 0) {
        translateY.value = event.translationY * 0.5; // Damping effect
      }
    },
    onEnd: (event) => {
      if (event.translationY > refreshThreshold && !refreshing.value) {
        refreshing.value = true;
        runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
        runOnJS(onRefresh)();
        
        // Reset after refresh
        setTimeout(() => {
          refreshing.value = false;
          translateY.value = withSpring(0, AnimationConfig.spring.bouncy);
        }, 1000);
      } else {
        translateY.value = withSpring(0, AnimationConfig.spring.bouncy);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[style, animatedStyle]}>
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

// Parallax Scroll Component
interface ParallaxScrollProps {
  children: ReactNode;
  parallaxFactor?: number;
  style?: ViewStyle;
}

export const ParallaxScroll: React.FC<ParallaxScrollProps> = ({
  children,
  parallaxFactor = 0.5,
  style,
}) => {
  const translateY = useSharedValue(0);

  const gestureHandler = useAnimatedGestureHandler({
    onActive: (event) => {
      translateY.value = event.translationY * parallaxFactor;
    },
    onEnd: () => {
      translateY.value = withSpring(0, AnimationConfig.spring.gentle);
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[style, animatedStyle]}>
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

export default {
  SwipeableCard,
  PressableScale,
  Draggable,
  PinchZoom,
  Rotatable,
  PullToRefresh,
  ParallaxScroll,
};
