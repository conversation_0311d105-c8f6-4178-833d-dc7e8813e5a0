{"name": "nutri-ai-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/cli-server-api": "^18.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~49.0.0", "expo-blur": "~12.9.0", "expo-dev-client": "~5.2.4", "expo-haptics": "~12.8.0", "expo-image-picker": "~14.7.0", "expo-linear-gradient": "~12.7.0", "expo-sensors": "~14.1.4", "expo-status-bar": "~1.11.0", "expo-updates": "~0.18.19", "fbjs": "^3.0.5", "invariant": "^2.2.4", "lottie-react-native": "^7.2.4", "metro-react-native-babel-transformer": "^0.77.0", "react": "19.0.0", "react-native": "^0.79.5", "react-native-gesture-handler": "~2.22.1", "react-native-haptic-feedback": "^2.3.3", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.8.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}