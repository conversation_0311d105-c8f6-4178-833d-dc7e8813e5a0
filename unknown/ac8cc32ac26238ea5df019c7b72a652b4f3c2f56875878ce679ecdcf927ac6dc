import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StatusBar,
  ImageBackground,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { useProfile } from '../contexts/ProfileContext';

// Removed width/height since we're using percentages for responsive design

const ProfileScreenNew: React.FC = () => {
  // Use profile context for dynamic data
  const { profile, updateProfile: updateProfileContext, dailyData, getHealthScore, calculateBMI } = useProfile();
  const [editingField, setEditingField] = useState<string | null>(null);

  // Settings states
  const [notifications, setNotifications] = useState(profile.notificationSettings.progressUpdates);
  const [darkMode, setDarkMode] = useState(false);
  const [mealReminders, setMealReminders] = useState(profile.notificationSettings.mealReminders);
  const [waterReminders, setWaterReminders] = useState(profile.notificationSettings.waterReminders);

  const updateProfile = (field: string, value: string | number | string[]) => {
    updateProfileContext(field as any, value);
    setEditingField(null);
  };

  const EditableField: React.FC<{
    label: string;
    value: string | number;
    field: string;
    type?: 'text' | 'number';
    suffix?: string;
  }> = ({ label, value, field, type = 'text', suffix = '' }) => {
    const [tempValue, setTempValue] = useState(value.toString());
    const isCurrentlyEditing = editingField === field;

    const handleSave = () => {
      const finalValue = type === 'number' ? parseFloat(tempValue) || 0 : tempValue;
      updateProfile(field, finalValue);
    };

    const handleCancel = () => {
      setTempValue(value.toString());
      setEditingField(null);
    };

    return (
      <View style={styles.editableField}>
        <Text style={styles.fieldLabel}>{label}</Text>
        <View style={styles.fieldContainer}>
          {isCurrentlyEditing ? (
            <View style={styles.editingContainer}>
              <TextInput
                style={styles.fieldInput}
                value={tempValue}
                onChangeText={setTempValue}
                keyboardType={type === 'number' ? 'numeric' : 'default'}
                autoFocus
                selectTextOnFocus
              />
              <View style={styles.editActions}>
                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                  <Ionicons name="checkmark" size={16} color="white" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                  <Ionicons name="close" size={16} color="#6B7C5A" />
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity 
              style={styles.fieldDisplay}
              onPress={() => setEditingField(field)}
            >
              <Text style={styles.fieldValue}>{value}{suffix}</Text>
              <Ionicons name="pencil" size={16} color="#6B7C5A" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      {/* Background */}
      <View style={styles.backgroundContainer}>
        <ImageBackground
          source={{ uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=800&q=80' }}
          style={styles.backgroundImage}
          imageStyle={styles.backgroundImageStyle}
        >
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.95)', 'rgba(255, 255, 255, 0.85)']}
            style={styles.whiteOverlay}
          />
        </ImageBackground>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Modern Profile Header */}
        <Animated.View entering={FadeInUp.duration(800)} style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{profile.name.charAt(0)}</Text>
            </View>
            <TouchableOpacity style={styles.editAvatarButton}>
              <Ionicons name="camera" size={16} color="white" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.profileInfo}>
            <EditableField
              label="Name"
              value={profile.name}
              field="name"
            />
            <EditableField
              label="Email"
              value={profile.email}
              field="email"
            />
          </View>
        </Animated.View>

        {/* Health Metrics - Editable */}
        <Animated.View entering={SlideInLeft.delay(200).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Health Metrics</Text>
          <View style={styles.metricsGrid}>
            <EditableField
              label="Age"
              value={profile.age}
              field="age"
              type="number"
              suffix=" years"
            />
            <EditableField
              label="Height"
              value={profile.height}
              field="height"
              type="number"
              suffix=" cm"
            />
            <EditableField
              label="Weight"
              value={profile.weight}
              field="weight"
              type="number"
              suffix=" kg"
            />
            <EditableField
              label="Target Weight"
              value={profile.targetWeight}
              field="targetWeight"
              type="number"
              suffix=" kg"
            />
          </View>
        </Animated.View>

        {/* Goals - Editable */}
        <Animated.View entering={SlideInRight.delay(400).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Goals</Text>
          <View style={styles.goalsGrid}>
            <EditableField
              label="Calories Goal"
              value={profile.caloriesGoal}
              field="caloriesGoal"
              type="number"
              suffix=" kcal"
            />
            <EditableField
              label="Protein Goal"
              value={profile.proteinGoal}
              field="proteinGoal"
              type="number"
              suffix=" g"
            />
            <EditableField
              label="Water Goal"
              value={profile.waterGoal}
              field="waterGoal"
              type="number"
              suffix=" glasses"
            />
            <EditableField
              label="Steps Goal"
              value={profile.stepsGoal}
              field="stepsGoal"
              type="number"
              suffix=" steps"
            />
          </View>
        </Animated.View>

        {/* Activity Level */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Activity Level</Text>
          <View style={styles.activityContainer}>
            {['Sedentary', 'Light', 'Moderate', 'Active', 'Very Active'].map((level, index) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.activityOption,
                  profile.activityLevel === level && styles.activityOptionSelected
                ]}
                onPress={() => updateProfile('activityLevel', level)}
              >
                <Text style={[
                  styles.activityText,
                  profile.activityLevel === level && styles.activityTextSelected
                ]}>
                  {level}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Dietary Preferences */}
        <Animated.View entering={SlideInLeft.delay(800).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Preferences</Text>
          <View style={styles.dietaryGrid}>
            {['Vegetarian', 'Vegan', 'Keto', 'Paleo', 'Mediterranean', 'Low Carb'].map((diet, index) => (
              <TouchableOpacity
                key={diet}
                style={[
                  styles.dietaryOption,
                  profile.dietaryPreferences?.includes(diet) && styles.dietaryOptionSelected
                ]}
                onPress={() => {
                  const current = profile.dietaryPreferences || [];
                  const updated = current.includes(diet)
                    ? current.filter((d: string) => d !== diet)
                    : [...current, diet];
                  updateProfile('dietaryPreferences', updated);
                }}
              >
                <Text style={[
                  styles.dietaryText,
                  profile.dietaryPreferences?.includes(diet) && styles.dietaryTextSelected
                ]}>
                  {diet}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Health Stats */}
        <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Health Statistics</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{profile.bmi}</Text>
              <Text style={styles.statLabel}>BMI</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{profile.bodyFatPercentage}%</Text>
              <Text style={styles.statLabel}>Body Fat</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{profile.muscleMass}kg</Text>
              <Text style={styles.statLabel}>Muscle Mass</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{profile.metabolicAge}</Text>
              <Text style={styles.statLabel}>Metabolic Age</Text>
            </View>
          </View>
        </Animated.View>

        {/* Achievements */}
        <Animated.View entering={SlideInRight.delay(1200).duration(600)} style={styles.section}>
          <Text style={styles.sectionTitle}>Achievements</Text>
          <View style={styles.achievementsGrid}>
            <View style={styles.achievementCard}>
              <Ionicons name="flame" size={24} color="#6B7C5A" />
              <Text style={styles.achievementValue}>{profile.currentStreak}</Text>
              <Text style={styles.achievementLabel}>Day Streak</Text>
            </View>
            <View style={styles.achievementCard}>
              <Ionicons name="restaurant" size={24} color="#6B7C5A" />
              <Text style={styles.achievementValue}>{profile.totalMealsLogged}</Text>
              <Text style={styles.achievementLabel}>Meals Logged</Text>
            </View>
            <View style={styles.achievementCard}>
              <Ionicons name="fitness" size={24} color="#6B7C5A" />
              <Text style={styles.achievementValue}>{profile.totalWorkouts}</Text>
              <Text style={styles.achievementLabel}>Workouts</Text>
            </View>
            <View style={styles.achievementCard}>
              <Ionicons name="trophy" size={24} color="#6B7C5A" />
              <Text style={styles.achievementValue}>{profile.longestStreak}</Text>
              <Text style={styles.achievementLabel}>Best Streak</Text>
            </View>
          </View>
        </Animated.View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundImage: {
    flex: 1,
  },
  backgroundImageStyle: {
    opacity: 0.3,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.92)',
  },
  scrollView: {
    flex: 1,
    paddingTop: 20, // Reduced for mobile app
  },
  profileHeader: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 32,
    marginBottom: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
  },
  avatarText: {
    fontSize: 36,
    fontWeight: '700',
    color: 'white',
    letterSpacing: -1,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  profileInfo: {
    width: '100%',
    gap: 16,
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 20,
    letterSpacing: -0.5,
  },
  metricsGrid: {
    gap: 16,
  },
  goalsGrid: {
    gap: 16,
  },
  editableField: {
    marginBottom: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    marginBottom: 8,
  },
  fieldContainer: {
    minHeight: 48,
  },
  fieldDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  fieldValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
  },
  editingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  fieldInput: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityContainer: {
    gap: 12,
  },
  activityOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  activityOptionSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  activityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },
  activityTextSelected: {
    color: 'white',
  },
  dietaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  dietaryOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  dietaryOptionSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  dietaryText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  dietaryTextSelected: {
    color: 'white',
  },
  bottomSpacing: {
    height: 100,
  },

  // Health Stats
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%', // Proper 2x2 grid
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Achievements
  achievementsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  achievementCard: {
    width: '48%', // Proper 2x2 grid
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    marginBottom: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  achievementValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginTop: 8,
    marginBottom: 4,
  },
  achievementLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Macro Targets
  macroContainer: {
    gap: 16,
  },
  macroItem: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  macroLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    marginBottom: 8,
  },
  macroValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
  },
  macroBar: {
    height: 8,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  macroFill: {
    height: '100%',
    borderRadius: 4,
  },
});

export default ProfileScreenNew;
