import React from 'react';
import { Text, TextStyle } from 'react-native';
import Animated, { FadeInUp } from 'react-native-reanimated';

interface AnimatedTextProps {
  text: string;
  style?: TextStyle;
  delay?: number;
}

const AnimatedText: React.FC<AnimatedTextProps> = ({ text, style, delay = 0 }) => {
  const words = text.split(' ');

  return (
    <Text style={style}>
      {words.map((word, index) => (
        <Animated.Text
          key={index}
          entering={FadeInUp.delay(delay + index * 50).duration(300)}
          style={style}
        >
          {word}{index < words.length - 1 ? ' ' : ''}
        </Animated.Text>
      ))}
    </Text>
  );
};

export default AnimatedText;
