import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';

interface ModernInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  variant?: 'default' | 'filled' | 'outline' | 'glass' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  secureTextEntry?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  disabled?: boolean;
  error?: string;
  success?: boolean;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  autoFocus?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send';
  onSubmitEditing?: () => void;
  maxLength?: number;
  borderRadius?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  animationDuration?: number;
  glowEffect?: boolean;
}

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);

export const ModernInput: React.FC<ModernInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  variant = 'default',
  size = 'md',
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  disabled = false,
  error,
  success = false,
  style,
  inputStyle,
  labelStyle,
  autoFocus = false,
  keyboardType = 'default',
  returnKeyType = 'done',
  onSubmitEditing,
  maxLength,
  borderRadius = 'lg',
  animationDuration = 200,
  glowEffect = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  const inputRef = useRef<TextInput>(null);

  const focusScale = useSharedValue(1);
  const borderOpacity = useSharedValue(0);
  const labelScale = useSharedValue(value ? 0.85 : 1);
  const labelTranslateY = useSharedValue(value ? -20 : 0);
  const glowOpacity = useSharedValue(0);

  const containerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: focusScale.value }],
  }));

  const borderAnimatedStyle = useAnimatedStyle(() => ({
    opacity: borderOpacity.value,
  }));

  const labelAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: labelScale.value },
      { translateY: labelTranslateY.value },
    ],
  }));

  const glowAnimatedStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const handleFocus = () => {
    setIsFocused(true);
    focusScale.value = withSpring(1.02, { damping: 15, stiffness: 300 });
    borderOpacity.value = withTiming(1, { duration: animationDuration });
    
    if (label && !value) {
      labelScale.value = withSpring(0.85, { damping: 15, stiffness: 300 });
      labelTranslateY.value = withSpring(-20, { damping: 15, stiffness: 300 });
    }
    
    if (glowEffect) {
      glowOpacity.value = withTiming(0.3, { duration: animationDuration });
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    focusScale.value = withSpring(1, { damping: 15, stiffness: 300 });
    borderOpacity.value = withTiming(0, { duration: animationDuration });
    
    if (label && !value) {
      labelScale.value = withSpring(1, { damping: 15, stiffness: 300 });
      labelTranslateY.value = withSpring(0, { damping: 15, stiffness: 300 });
    }
    
    if (glowEffect) {
      glowOpacity.value = withTiming(0, { duration: animationDuration * 2 });
    }
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const getContainerStyle = () => {
    return [
      styles.container,
      styles[`${variant}Container`],
      styles[`${size}Container`],
      styles[`${borderRadius}BorderRadius`],
      isFocused && styles.focused,
      error && styles.error,
      success && styles.success,
      disabled && styles.disabled,
      style,
    ];
  };

  const getInputStyle = () => {
    return [
      styles.input,
      styles[`${size}Input`],
      styles[`${variant}Input`],
      multiline && styles.multilineInput,
      inputStyle,
    ];
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 16;
      case 'lg': return 24;
      default: return 20;
    }
  };

  const getIconColor = () => {
    if (disabled) return Colors.mutedForeground;
    if (error) return Colors.error;
    if (success) return Colors.success;
    if (isFocused) return Colors.brand;
    return Colors.mutedForeground;
  };

  return (
    <View style={styles.wrapper}>
      {label && variant !== 'minimal' && (
        <Animated.Text style={[styles.label, styles[`${size}Label`], labelStyle, labelAnimatedStyle]}>
          {label}
        </Animated.Text>
      )}
      
      <Animated.View style={[containerAnimatedStyle]}>
        {glowEffect && (
          <Animated.View style={[getContainerStyle(), styles.glowContainer, glowAnimatedStyle]} />
        )}
        
        <View style={getContainerStyle()}>
          <Animated.View style={[styles.focusBorder, borderAnimatedStyle]} />
          
          <View style={styles.inputContainer}>
            {leftIcon && (
              <View style={styles.leftIconContainer}>
                <Ionicons
                  name={leftIcon}
                  size={getIconSize()}
                  color={getIconColor()}
                />
              </View>
            )}
            
            <AnimatedTextInput
              ref={inputRef}
              style={getInputStyle()}
              value={value}
              onChangeText={onChangeText}
              placeholder={placeholder}
              placeholderTextColor={Colors.mutedForeground}
              secureTextEntry={secureTextEntry && !isPasswordVisible}
              multiline={multiline}
              numberOfLines={numberOfLines}
              editable={!disabled}
              autoFocus={autoFocus}
              keyboardType={keyboardType}
              returnKeyType={returnKeyType}
              onSubmitEditing={onSubmitEditing}
              maxLength={maxLength}
              onFocus={handleFocus}
              onBlur={handleBlur}
            />
            
            {(rightIcon || secureTextEntry) && (
              <TouchableOpacity
                style={styles.rightIconContainer}
                onPress={secureTextEntry ? togglePasswordVisibility : onRightIconPress}
                disabled={disabled}
              >
                <Ionicons
                  name={
                    secureTextEntry
                      ? isPasswordVisible
                        ? 'eye-off'
                        : 'eye'
                      : rightIcon!
                  }
                  size={getIconSize()}
                  color={getIconColor()}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Animated.View>
      
      {error && (
        <Text style={[styles.errorText, styles[`${size}ErrorText`]]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: Spacing.md,
  },
  
  // Container Styles
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  
  // Variant Styles
  defaultContainer: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  filledContainer: {
    backgroundColor: Colors.muted,
    borderWidth: 0,
  },
  outlineContainer: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.border,
  },
  glassContainer: {
    backgroundColor: Colors.glass,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
  },
  minimalContainer: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    borderRadius: 0,
  },
  
  // Size Styles
  smContainer: {
    minHeight: 36,
    paddingHorizontal: Spacing.md,
  },
  mdContainer: {
    minHeight: 44,
    paddingHorizontal: Spacing.lg,
  },
  lgContainer: {
    minHeight: 52,
    paddingHorizontal: Spacing.xl,
  },
  
  // Border Radius
  smBorderRadius: { borderRadius: BorderRadius.sm },
  mdBorderRadius: { borderRadius: BorderRadius.md },
  lgBorderRadius: { borderRadius: BorderRadius.lg },
  xlBorderRadius: { borderRadius: BorderRadius.xl },
  fullBorderRadius: { borderRadius: BorderRadius.full },
  
  // Input Container
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  // Input Styles
  input: {
    flex: 1,
    fontWeight: FontWeights.normal,
    color: Colors.foreground,
    paddingVertical: 0,
  },
  
  smInput: {
    fontSize: FontSizes.sm,
  },
  mdInput: {
    fontSize: FontSizes.base,
  },
  lgInput: {
    fontSize: FontSizes.lg,
  },
  
  defaultInput: {},
  filledInput: {},
  outlineInput: {},
  glassInput: {},
  minimalInput: {},
  
  multilineInput: {
    textAlignVertical: 'top',
    paddingTop: Spacing.md,
    paddingBottom: Spacing.md,
  },
  
  // Label Styles
  label: {
    position: 'absolute',
    left: Spacing.lg,
    top: '50%',
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    zIndex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: Spacing.xs,
  },
  
  smLabel: {
    fontSize: FontSizes.sm,
  },
  mdLabel: {
    fontSize: FontSizes.base,
  },
  lgLabel: {
    fontSize: FontSizes.lg,
  },
  
  // Icon Containers
  leftIconContainer: {
    marginRight: Spacing.md,
  },
  rightIconContainer: {
    marginLeft: Spacing.md,
    padding: Spacing.xs,
  },
  
  // Focus Border
  focusBorder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 2,
    borderColor: Colors.brand,
    borderRadius: BorderRadius.lg,
  },
  
  // Glow Effect
  glowContainer: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    backgroundColor: Colors.brandGlow,
    borderRadius: BorderRadius.xl,
  },
  
  // States
  focused: {
    borderColor: Colors.brand,
  },
  error: {
    borderColor: Colors.error,
  },
  success: {
    borderColor: Colors.success,
  },
  disabled: {
    opacity: 0.5,
    backgroundColor: Colors.muted,
  },
  
  // Error Text
  errorText: {
    color: Colors.error,
    fontWeight: FontWeights.medium,
    marginTop: Spacing.xs,
    marginLeft: Spacing.lg,
  },
  
  smErrorText: {
    fontSize: FontSizes.xs,
  },
  mdErrorText: {
    fontSize: FontSizes.sm,
  },
  lgErrorText: {
    fontSize: FontSizes.base,
  },
});
