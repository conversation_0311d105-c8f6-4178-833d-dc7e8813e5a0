// Dynamic Profile Service - No more static data!
// This provides default values for new users only

export interface UserProfile {
  // Basic Info
  name: string;
  email: string;
  age: number;
  avatarUrl: string;
  isProfileComplete: boolean;

  // Physical Metrics
  height: number; // cm
  weight: number; // kg
  targetWeight: number; // kg

  // Daily Goals
  caloriesGoal: number;
  proteinGoal: number; // grams
  waterGoal: number; // glasses
  stepsGoal: number;

  // Activity & Preferences
  activityLevel: string;
  dietaryPreferences: string[];
  dietaryGoal: string;
  dietaryType: string;

  // Detailed Preferences
  allergies: string[];
  dislikedIngredients: string[];
  preferredCuisines: string[];
  healthGoals: string[];
  dietaryRestrictions: string[];

  // Progress & Stats (calculated dynamically)
  dailyProgress: number; // percentage
  weeklyProgress: number;
  monthlyProgress: number;

  // Additional Health Data (calculated)
  bmi: number;
  bodyFatPercentage: number;
  muscleMass: number; // kg
  metabolicAge: number;

  // Preferences & Settings
  preferredMealTimes: {
    breakfast: string;
    lunch: string;
    dinner: string;
    snack: string;
  };
  notificationSettings: {
    mealReminders: boolean;
    waterReminders: boolean;
    workoutReminders: boolean;
    progressUpdates: boolean;
  };

  // Achievements & Streaks (calculated from real data)
  currentStreak: number; // days
  longestStreak: number;
  totalMealsLogged: number;
  totalWorkouts: number;

  // Macro Preferences
  macroTargets: {
    carbs: number; // percentage
    protein: number;
    fat: number;
  };

  // Timestamps
  createdAt: string;
  updatedAt: string;
}

// Default profile for new users - only used for initialization
export const getDefaultProfile = (): UserProfile => ({
  // Basic Info - Empty for new users
  name: "",
  email: "",
  age: 0,
  avatarUrl: "",
  isProfileComplete: false,

  // Physical Metrics - Defaults
  height: 170,
  weight: 70,
  targetWeight: 65,

  // Daily Goals - Reasonable defaults
  caloriesGoal: 2000,
  proteinGoal: 120,
  waterGoal: 8,
  stepsGoal: 10000,

  // Activity & Preferences - Empty
  activityLevel: "Moderate",
  dietaryPreferences: [],
  dietaryGoal: "",
  dietaryType: "",

  // Detailed Preferences - Empty
  allergies: [],
  dislikedIngredients: [],
  preferredCuisines: [],
  healthGoals: [],
  dietaryRestrictions: [],

  // Progress & Stats - Start at zero
  dailyProgress: 0,
  weeklyProgress: 0,
  monthlyProgress: 0,

  // Additional Health Data - Calculated
  bmi: 24.2, // Will be calculated from height/weight
  bodyFatPercentage: 0,
  muscleMass: 0,
  metabolicAge: 0,

  // Preferences & Settings - Defaults
  preferredMealTimes: {
    breakfast: "08:00",
    lunch: "12:30",
    dinner: "19:00",
    snack: "15:30"
  },
  notificationSettings: {
    mealReminders: true,
    waterReminders: true,
    workoutReminders: false,
    progressUpdates: true
  },

  // Achievements & Streaks - Start at zero
  currentStreak: 0,
  longestStreak: 0,
  totalMealsLogged: 0,
  totalWorkouts: 0,

  // Macro Preferences - Balanced defaults
  macroTargets: {
    carbs: 45,
    protein: 30,
    fat: 25
  },

  // Timestamps
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

// For backward compatibility - will be removed once all components use ProfileContext
export const userProfile = getDefaultProfile();
