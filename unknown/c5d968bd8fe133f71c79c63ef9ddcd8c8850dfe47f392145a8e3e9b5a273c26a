import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  withSpring,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../constants/Colors';

interface ModernLoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'wave' | 'shimmer' | 'skeleton';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: string;
  text?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullScreen?: boolean;
  overlay?: boolean;
  duration?: number;
}

export const ModernLoading: React.FC<ModernLoadingProps> = ({
  variant = 'spinner',
  size = 'md',
  color = Colors.brand,
  text,
  style,
  textStyle,
  fullScreen = false,
  overlay = false,
  duration = 1000,
}) => {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const translateX = useSharedValue(0);

  useEffect(() => {
    switch (variant) {
      case 'spinner':
        rotation.value = withRepeat(
          withTiming(360, { duration, easing: Easing.linear }),
          -1,
          false
        );
        break;
      case 'pulse':
        scale.value = withRepeat(
          withSequence(
            withTiming(1.2, { duration: duration / 2 }),
            withTiming(1, { duration: duration / 2 })
          ),
          -1,
          false
        );
        break;
      case 'wave':
        translateX.value = withRepeat(
          withSequence(
            withTiming(20, { duration: duration / 2 }),
            withTiming(-20, { duration: duration / 2 })
          ),
          -1,
          false
        );
        break;
      case 'shimmer':
        translateX.value = withRepeat(
          withTiming(100, { duration, easing: Easing.linear }),
          -1,
          false
        );
        break;
    }
  }, [variant, duration]);

  const spinnerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  const pulseAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: interpolate(scale.value, [1, 1.2], [1, 0.7]),
  }));

  const waveAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const shimmerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const getSize = () => {
    switch (size) {
      case 'sm': return 24;
      case 'lg': return 48;
      case 'xl': return 64;
      default: return 32;
    }
  };

  const renderSpinner = () => (
    <Animated.View style={[styles.spinner, { width: getSize(), height: getSize() }, spinnerAnimatedStyle]}>
      <View style={[styles.spinnerBorder, { borderColor: color, borderTopColor: 'transparent' }]} />
    </Animated.View>
  );

  const renderDots = () => {
    const dotSize = getSize() / 4;
    return (
      <View style={styles.dotsContainer}>
        {[0, 1, 2].map((index) => (
          <DotComponent key={index} delay={index * 200} size={dotSize} color={color} />
        ))}
      </View>
    );
  };

  const renderPulse = () => (
    <Animated.View style={[styles.pulse, { width: getSize(), height: getSize(), backgroundColor: color }, pulseAnimatedStyle]} />
  );

  const renderWave = () => (
    <View style={styles.waveContainer}>
      {[0, 1, 2, 3, 4].map((index) => (
        <WaveBar key={index} delay={index * 100} height={getSize()} color={color} />
      ))}
    </View>
  );

  const renderShimmer = () => (
    <View style={[styles.shimmerContainer, { height: getSize() }]}>
      <View style={[styles.shimmerBackground, { backgroundColor: Colors.muted }]} />
      <Animated.View style={[styles.shimmerOverlay, shimmerAnimatedStyle]}>
        <LinearGradient
          colors={['transparent', Colors.background, 'transparent']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.shimmerGradient}
        />
      </Animated.View>
    </View>
  );

  const renderSkeleton = () => (
    <View style={styles.skeletonContainer}>
      <SkeletonLine width="100%" height={12} />
      <SkeletonLine width="80%" height={12} />
      <SkeletonLine width="60%" height={12} />
    </View>
  );

  const renderContent = () => {
    switch (variant) {
      case 'spinner': return renderSpinner();
      case 'dots': return renderDots();
      case 'pulse': return renderPulse();
      case 'wave': return renderWave();
      case 'shimmer': return renderShimmer();
      case 'skeleton': return renderSkeleton();
      default: return renderSpinner();
    }
  };

  const containerStyle = [
    styles.container,
    fullScreen && styles.fullScreen,
    overlay && styles.overlay,
    style,
  ];

  return (
    <View style={containerStyle}>
      {renderContent()}
      {text && (
        <Text style={[styles.text, styles[`${size}Text`], textStyle]}>
          {text}
        </Text>
      )}
    </View>
  );
};

// Dot Component for dots variant
const DotComponent: React.FC<{ delay: number; size: number; color: string }> = ({ delay, size, color }) => {
  const scale = useSharedValue(1);

  useEffect(() => {
    scale.value = withRepeat(
      withSequence(
        withTiming(1.5, { duration: 400 }),
        withTiming(1, { duration: 400 })
      ),
      -1,
      false
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }), []);

  return (
    <Animated.View
      style={[
        styles.dot,
        {
          width: size,
          height: size,
          backgroundColor: color,
          borderRadius: size / 2,
        },
        animatedStyle,
      ]}
    />
  );
};

// Wave Bar Component for wave variant
const WaveBar: React.FC<{ delay: number; height: number; color: string }> = ({ delay, height, color }) => {
  const scaleY = useSharedValue(1);

  useEffect(() => {
    scaleY.value = withRepeat(
      withSequence(
        withTiming(0.3, { duration: 300 }),
        withTiming(1, { duration: 300 })
      ),
      -1,
      false
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scaleY: scaleY.value }],
  }), []);

  return (
    <Animated.View
      style={[
        styles.waveBar,
        {
          height,
          backgroundColor: color,
        },
        animatedStyle,
      ]}
    />
  );
};

// Skeleton Line Component
const SkeletonLine: React.FC<{ width: string; height: number }> = ({ width, height }) => {
  const opacity = useSharedValue(1);

  useEffect(() => {
    opacity.value = withRepeat(
      withSequence(
        withTiming(0.3, { duration: 800 }),
        withTiming(1, { duration: 800 })
      ),
      -1,
      false
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }), []);

  return (
    <Animated.View
      style={[
        styles.skeletonLine,
        {
          width,
          height,
          backgroundColor: Colors.muted,
        },
        animatedStyle,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  
  // Spinner
  spinner: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  spinnerBorder: {
    width: '100%',
    height: '100%',
    borderWidth: 3,
    borderRadius: 999,
  },
  
  // Dots
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    marginHorizontal: Spacing.xs,
  },
  
  // Pulse
  pulse: {
    borderRadius: 999,
  },
  
  // Wave
  waveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  waveBar: {
    width: 4,
    marginHorizontal: 2,
    borderRadius: 2,
  },
  
  // Shimmer
  shimmerContainer: {
    width: '100%',
    overflow: 'hidden',
    borderRadius: BorderRadius.md,
    position: 'relative',
  },
  shimmerBackground: {
    width: '100%',
    height: '100%',
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: -100,
    width: 100,
    height: '100%',
  },
  shimmerGradient: {
    flex: 1,
  },
  
  // Skeleton
  skeletonContainer: {
    width: '100%',
  },
  skeletonLine: {
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.md,
  },
  
  // Text
  text: {
    marginTop: Spacing.lg,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  smText: {
    fontSize: FontSizes.sm,
  },
  mdText: {
    fontSize: FontSizes.base,
  },
  lgText: {
    fontSize: FontSizes.lg,
  },
  xlText: {
    fontSize: FontSizes.xl,
  },
});
