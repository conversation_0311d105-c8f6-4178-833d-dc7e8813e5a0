/**
 * REAL Voice Recognition Service - NO DEMO MODE
 *
 * This service ONLY provides REAL voice-to-text conversion.
 * If real voice recognition is not available, it will fail gracefully.
 *
 * SUPPORTED PLATFORMS:
 * ✅ Web browsers (Chrome, Safari, Edge) - Web Speech API
 * ✅ Mobile browsers with speech support
 * ❌ Expo Go native app (not supported - will show error)
 *
 * FOR MOBILE REAL VOICE RECOGNITION:
 * - Build with EAS Build: npx eas build --platform android
 * - Or use web version: Press 'w' in Expo CLI
 */
import { Platform, Alert } from 'react-native';

export interface VoiceRecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
}

export interface VoiceServiceCallbacks {
  onStart?: () => void;
  onResult?: (result: VoiceRecognitionResult) => void;
  onEnd?: () => void;
  onError?: (error: string) => void;
  onPartialResult?: (text: string) => void;
}

class VoiceService {
  private static isListening = false;
  private static callbacks: VoiceServiceCallbacks = {};
  private static currentRecognition: any = null;

  static async initialize(): Promise<boolean> {
    try {
      // Check for REAL Web Speech API support
      if (this.isWebSpeechAvailable()) {
        console.log('✅ REAL Web Speech API detected and ready');
        return true;
      }

      // NO DEMO MODE - Only real voice recognition
      console.error('❌ REAL voice recognition not available');
      console.log('💡 To enable REAL voice recognition:');
      console.log('   1. Open in web browser: Press "w" in Expo CLI');
      console.log('   2. Use Chrome, Safari, or Edge browser');
      console.log('   3. Or build native app: npx eas build --platform android');

      return false; // Return false to indicate voice is not available
    } catch (error) {
      console.error('Error initializing voice service:', error);
      return false;
    }
  }

  static async startListening(callbacks: VoiceServiceCallbacks = {}): Promise<boolean> {
    try {
      if (this.isListening) {
        await this.stopListening();
      }

      this.callbacks = callbacks;

      // ONLY REAL VOICE RECOGNITION - NO DEMO MODE
      if (this.isWebSpeechAvailable()) {
        console.log('🎤 ✅ Starting REAL Web Speech API voice recognition');
        return this.startWebSpeechRecognition();
      }

      // NO FALLBACK TO DEMO - FAIL GRACEFULLY
      console.error('❌ REAL voice recognition not available on this platform');
      console.log('💡 To use REAL voice recognition:');
      console.log('   1. Open in web browser: Press "w" in Expo CLI');
      console.log('   2. Use Chrome, Safari, or Edge');
      console.log('   3. Allow microphone permissions');

      this.callbacks.onError?.('Voice recognition not available. Please use web browser or build native app.');
      return false;
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      this.callbacks.onError?.('Failed to start voice recognition');
      return false;
    }
  }

  private static isWebSpeechAvailable(): boolean {
    try {
      // Check if we're in a browser environment with Web Speech API
      return (
        typeof window !== 'undefined' &&
        ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)
      );
    } catch {
      return false;
    }
  }

  private static isBrowserEnvironment(): boolean {
    try {
      // Check if we're in any browser-like environment
      return (
        typeof window !== 'undefined' &&
        typeof navigator !== 'undefined' &&
        typeof document !== 'undefined'
      );
    } catch {
      return false;
    }
  }

  private static startWebSpeechRecognition(): boolean {
    try {
      // @ts-ignore - Web Speech API types
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.error('❌ Web Speech API not supported in this browser');
        console.log('💡 Try Chrome, Safari, or Edge for voice recognition');
        this.callbacks.onError?.('Voice recognition not supported in this browser. Try Chrome or Safari.');
        return false;
      }

      console.log('✅ REAL Web Speech API detected - starting voice recognition');

      const recognition = new SpeechRecognition();

      // Configure for optimal performance
      recognition.continuous = false;           // Stop after one phrase
      recognition.interimResults = true;       // Show partial results
      recognition.lang = 'en-US';             // English language
      recognition.maxAlternatives = 1;        // Only best result

      // Store recognition instance for cleanup
      this.currentRecognition = recognition;

      recognition.onstart = () => {
        console.log('🎤 ✅ REAL voice recognition started successfully');
        this.isListening = true;
        this.callbacks.onStart?.();
      };

      recognition.onresult = (event: any) => {
        console.log('🎤 📝 Processing voice input...');

        const results = event.results;
        const lastResult = results[results.length - 1];
        const transcript = lastResult[0].transcript.trim();
        const confidence = lastResult[0].confidence || 0.9;
        const isFinal = lastResult.isFinal;

        console.log(`🎤 Voice transcript: "${transcript}" (confidence: ${Math.round(confidence * 100)}%, final: ${isFinal})`);

        if (isFinal) {
          console.log('✅ Final voice result:', transcript);
          this.callbacks.onResult?.({
            text: transcript,
            confidence,
            isFinal: true
          });
          this.isListening = false;
        } else {
          // Show partial results in real-time
          this.callbacks.onPartialResult?.(transcript);
        }
      };

      recognition.onend = () => {
        console.log('🎤 Voice recognition session ended');
        this.isListening = false;
        this.callbacks.onEnd?.();
        this.currentRecognition = null;
      };

      recognition.onerror = (event: any) => {
        console.error('🎤 ❌ Voice recognition error:', event.error);
        this.isListening = false;

        let errorMessage = 'Voice recognition failed';
        switch (event.error) {
          case 'no-speech':
            errorMessage = 'No speech detected. Please try again.';
            break;
          case 'audio-capture':
            errorMessage = 'Microphone not accessible. Please check permissions.';
            break;
          case 'not-allowed':
            errorMessage = 'Microphone permission denied. Please allow microphone access.';
            break;
          case 'network':
            errorMessage = 'Network error. Please check your connection.';
            break;
          default:
            errorMessage = `Voice recognition error: ${event.error}`;
        }

        this.callbacks.onError?.(errorMessage);
        this.currentRecognition = null;
      };

      // Start recognition
      recognition.start();
      console.log('🎤 Voice recognition request sent to browser...');
      return true;

    } catch (error) {
      console.error('❌ Failed to start Web Speech API:', error);
      this.callbacks.onError?.('Failed to initialize voice recognition');
      return false;
    }
  }

  // NO DEMO MODE - ONLY REAL VOICE RECOGNITION

  static async stopListening(): Promise<void> {
    try {
      console.log('🎤 Stopping voice recognition...');

      // Stop Web Speech API if active
      if (this.currentRecognition) {
        this.currentRecognition.stop();
        this.currentRecognition = null;
        console.log('🎤 Web Speech API stopped');
      }

      this.isListening = false;
      this.callbacks.onEnd?.();
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
    }
  }

  static async cancelListening(): Promise<void> {
    try {
      console.log('🎤 Fallback: Voice recognition cancelled');
      this.isListening = false;
      this.callbacks.onEnd?.();
    } catch (error) {
      console.error('Error canceling voice recognition:', error);
    }
  }

  static isCurrentlyListening(): boolean {
    return this.isListening;
  }

  // Fallback event handlers (not used in Expo Go version)
  // These would be used in a production build with native voice recognition

  // Utility methods
  static async checkPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // Android permissions are handled automatically by the library
        return true;
      } else if (Platform.OS === 'ios') {
        // iOS permissions are handled automatically by the library
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking voice permissions:', error);
      return false;
    }
  }

  static async requestPermissions(): Promise<boolean> {
    try {
      // Permissions are requested automatically when starting voice recognition
      return await this.checkPermissions();
    } catch (error) {
      console.error('Error requesting voice permissions:', error);
      return false;
    }
  }

  // Text-to-speech functionality (for voice responses)
  static async speak(text: string): Promise<void> {
    try {
      // Note: For TTS, you might want to use a different library like react-native-tts
      // This is a placeholder for future TTS implementation
      console.log('🔊 Would speak:', text);
      
      // For now, we'll just log the text
      // In a full implementation, you'd use:
      // import Tts from 'react-native-tts';
      // await Tts.speak(text);
    } catch (error) {
      console.error('Error speaking text:', error);
    }
  }

  // Clean up resources
  static async destroy(): Promise<void> {
    try {
      console.log('🎤 Fallback: Voice service destroyed');
      this.isListening = false;
      this.callbacks = {};
    } catch (error) {
      console.error('Error destroying voice service:', error);
    }
  }

  // Voice command processing
  static processVoiceCommand(text: string): {
    intent: string;
    entities: Record<string, any>;
    confidence: number;
  } {
    const lowerText = text.toLowerCase().trim();
    
    // Simple intent recognition (in production, you'd use a more sophisticated NLP service)
    if (lowerText.includes('scan') || lowerText.includes('analyze') || lowerText.includes('photo')) {
      return {
        intent: 'scan_food',
        entities: {},
        confidence: 0.8
      };
    }
    
    if (lowerText.includes('recipe') || lowerText.includes('cook') || lowerText.includes('make')) {
      const foodMatch = lowerText.match(/recipe for (.+)|make (.+)|cook (.+)/);
      return {
        intent: 'find_recipe',
        entities: {
          food: foodMatch ? (foodMatch[1] || foodMatch[2] || foodMatch[3]) : null
        },
        confidence: 0.8
      };
    }
    
    if (lowerText.includes('meal plan') || lowerText.includes('plan') || lowerText.includes('week')) {
      return {
        intent: 'create_meal_plan',
        entities: {},
        confidence: 0.8
      };
    }
    
    if (lowerText.includes('nutrition') || lowerText.includes('calories') || lowerText.includes('protein')) {
      return {
        intent: 'nutrition_question',
        entities: {
          question: text
        },
        confidence: 0.9
      };
    }
    
    // Default to general question
    return {
      intent: 'general_question',
      entities: {
        question: text
      },
      confidence: 0.6
    };
  }
}

export default VoiceService;
